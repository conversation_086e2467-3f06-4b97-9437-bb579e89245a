# Generated by Django 5.1.7 on 2025-06-26 19:49

from django.db import migrations


def fix_required_ingredients(apps, schema_editor):
    """
    Ryd op i required_ingredients - fjern ingredienser fra ingredient groups
    og behold kun påkrævede DrinkIngredients
    """
    Drink = apps.get_model('bar', 'Drink')
    DrinkIngredient = apps.get_model('bar', 'DrinkIngredient')

    for drink in Drink.objects.all():
        # Få kun påkrævede ingredienser fra DrinkIngredient
        required_ingredients = [
            di.ingredient for di in drink.drink_ingredients.filter(is_required=True)
        ]

        # Sæt required_ingredients til kun at indeholde påkrævede DrinkIngredients
        drink.required_ingredients.set(required_ingredients)


def reverse_fix_required_ingredients(apps, schema_editor):
    """Reverse operation - ikke nødvendig da vi bare rydder op"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0015_drinkingredient_is_required_and_more'),
    ]

    operations = [
        migrations.RunPython(fix_required_ingredients, reverse_fix_required_ingredients),
    ]
