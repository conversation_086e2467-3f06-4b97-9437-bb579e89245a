# Generated by Django 5.1.7 on 2025-06-26 17:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0011_drinkplan_drinkplanitem'),
    ]

    operations = [
        migrations.CreateModel(
            name='DrinkIngredient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=1, help_text='Mængde i cl/ml', max_digits=6)),
                ('unit', models.CharField(choices=[('cl', 'Centiliter'), ('ml', 'Milliliter'), ('stk', 'Stykker'), ('tsk', 'Teskeer'), ('spsk', 'Spiseskeer')], default='cl', max_length=10)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drink_ingredients', to='bar.drink')),
                ('ingredient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bar.ingredient')),
            ],
            options={
                'unique_together': {('drink', 'ingredient')},
            },
        ),
    ]
