# Generated by Django 5.1.7 on 2025-06-26 16:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0010_comment_rating'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DrinkPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Navn på din fest/event', max_length=100)),
                ('guest_count', models.PositiveIntegerField(help_text='Antal gæster')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DrinkPlanItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('servings_per_guest', models.DecimalField(decimal_places=1, default=1.0, help_text='Hvor mange af denne drink per gæst', max_digits=3)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bar.drink')),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='bar.drinkplan')),
            ],
        ),
    ]
