# Generated by Django 5.2 on 2025-05-06 17:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='drink',
            name='ingredients',
        ),
        migrations.CreateModel(
            name='IngredientGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('ingredients', models.ManyToManyField(to='bar.ingredient')),
            ],
        ),
        migrations.AddField(
            model_name='drink',
            name='ingredient_groups',
            field=models.ManyToManyField(to='bar.ingredientgroup'),
        ),
    ]
