# Generated by Django 5.1.7 on 2025-06-26 19:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0014_ingredient_substitutes'),
    ]

    operations = [
        migrations.AddField(
            model_name='drinkingredient',
            name='is_required',
            field=models.BooleanField(default=True, help_text='<PERSON><PERSON><PERSON> hvis denne ingrediens er påkrævet for drinken', verbose_name='Påkrævet'),
        ),
        migrations.AlterField(
            model_name='ingredient',
            name='substitutes',
            field=models.ManyToManyField(blank=True, help_text='Ingredienser der kan erstatte denne', to='bar.ingredient', verbose_name='Erstatninger'),
        ),
    ]
