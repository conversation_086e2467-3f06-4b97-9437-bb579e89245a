# Generated by Django 5.2 on 2025-05-06 17:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0002_remove_drink_ingredients_ingredientgroup_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='drink',
            name='required_ingredients',
            field=models.ManyToManyField(blank=True, to='bar.ingredient'),
        ),
        migrations.AlterField(
            model_name='drink',
            name='ingredient_groups',
            field=models.ManyToManyField(blank=True, to='bar.ingredientgroup'),
        ),
    ]
