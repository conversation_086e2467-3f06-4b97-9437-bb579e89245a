# Generated by Django 5.1.7 on 2025-06-26 20:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0017_auto_20250626_2200'),
    ]

    operations = [
        migrations.CreateModel(
            name='DrinkIngredientGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=1, help_text='Mængde i cl/ml', max_digits=6)),
                ('unit', models.CharField(choices=[('cl', 'Centiliter'), ('ml', 'Milliliter'), ('stk', 'Stykker'), ('tsk', 'Teskeer'), ('spsk', 'Spiseskeer')], default='cl', max_length=10)),
                ('is_required', models.BooleanField(default=True, help_text='<PERSON><PERSON><PERSON> hvis denne ingrediens gruppe er påkrævet for drinken', verbose_name='Påkrævet')),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drink_ingredient_groups', to='bar.drink')),
                ('ingredient_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bar.ingredientgroup')),
            ],
            options={
                'unique_together': {('drink', 'ingredient_group')},
            },
        ),
    ]
