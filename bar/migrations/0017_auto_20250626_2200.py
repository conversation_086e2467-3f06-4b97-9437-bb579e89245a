# Generated by Django 5.1.7 on 2025-06-26 20:00

from django.db import migrations


def clean_required_ingredients_final(apps, schema_editor):
    """
    Final cleanup - fjern alle ingredienser fra ingredient groups fra required_ingredients
    """
    Drink = apps.get_model('bar', 'Drink')
    DrinkIngredient = apps.get_model('bar', 'DrinkIngredient')

    for drink in Drink.objects.all():
        # Få kun påkrævede ingredienser fra DrinkIngredient (ikke fra groups)
        required_ingredients = [
            di.ingredient for di in drink.drink_ingredients.filter(is_required=True)
        ]

        # Sæt required_ingredients til kun at indeholde påkrævede DrinkIngredients
        drink.required_ingredients.set(required_ingredients)
        print(f"Cleaned {drink.name}: now has {len(required_ingredients)} required ingredients")


def reverse_clean(apps, schema_editor):
    """Reverse operation"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0016_auto_20250626_2149'),
    ]

    operations = [
        migrations.RunPython(clean_required_ingredients_final, reverse_clean),
    ]
