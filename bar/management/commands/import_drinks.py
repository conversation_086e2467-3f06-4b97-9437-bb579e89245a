import requests
from bs4 import BeautifulSoup
import re
import os
import time
from urllib.parse import urljoin, urlparse
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.db import transaction
from bar.models import Drink, Ingredient, DrinkIngredient
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import drinks from Shake-it website automatically'
    
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Ingredienser der IKKE skal importeres (ignoreres helt)
        self.ignore_keywords = [
            'isterninger', 'is', 'ice', 'ice cubes', 'glas', 'glass',
            'vinglas', 'cocktailglas', 'highball', 'lowball'
        ]

        # Ingredienser der typisk er optional (kan tilpasses)
        self.optional_keywords = [
            'salt', 'sukker', 'lime', 'citron', 'appelsin', 'garnish',
            'pynt', 'twist', 'skive', 'stykker', 'dråber', 'splash',
            'rim', 'kant', 'decoration', 'topping', 'limebåd', 'citronskive',
            'appelsinskive', 'oliven', 'cocktailbær', 'mynteblade'
        ]

        # Ingredienser der altid er påkrævet (hovedingredienser)
        self.required_keywords = [
            'vodka', 'gin', 'rum', 'whiskey', 'tequila', 'bourbon', 'cognac',
            'campari', 'aperol', 'vermouth', 'likør', 'sirup', 'juice',
            'bitter', 'danskvand', 'sodavand', 'tonic'
        ]

    def add_arguments(self, parser):
        parser.add_argument(
            '--url',
            type=str,
            help='Specific Shake-it drink URL to import'
        )
        parser.add_argument(
            '--category',
            type=str,
            help='Import all drinks from a category (e.g., cocktails, shots)'
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=10,
            help='Maximum number of drinks to import'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be imported without actually importing'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🍹 Starting Shake-it drink import...'))
        
        if options['url']:
            self.import_single_drink(options['url'], options['dry_run'])
        elif options['category']:
            self.import_category(options['category'], options['limit'], options['dry_run'])
        else:
            self.stdout.write(self.style.ERROR('Please specify --url or --category'))

    def import_single_drink(self, url, dry_run=False):
        """Import a single drink from URL"""
        try:
            drink_data = self.scrape_drink(url)
            if drink_data:
                # Validate that we got essential data (only name required now)
                if not drink_data.get('name') or drink_data['name'] == 'Unknown Drink':
                    self.stdout.write(
                        self.style.ERROR(f'❌ Could not extract drink name from {url}')
                    )
                    return

                if dry_run:
                    self.print_drink_preview(drink_data)
                else:
                    self.save_drink(drink_data)
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Imported: {drink_data["name"]}')
                    )
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ No data extracted from {url}')
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to import {url}: {str(e)}')
            )

    def scrape_drink(self, url):
        """Scrape drink data from Shake-it URL"""
        self.stdout.write(f'🔍 Scraping: {url}')
        
        response = self.session.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract drink data - ONLY name and image, you handle the rest manually
        drink_data = {
            'name': self.extract_name(soup),
            'image_url': self.extract_image_url(soup, url),
            'source_url': url,
            'drink_type': self.determine_drink_type(soup)
        }
        
        return drink_data

    def extract_name(self, soup):
        """Extract drink name from Shake-it"""
        # Look for the main heading - Shake-it uses h1 for drink names
        h1_elements = soup.find_all('h1')
        for h1 in h1_elements:
            text = h1.get_text().strip()
            # Skip if it's just "Frisk" or other descriptors
            if text and len(text) > 2 and text not in ['Frisk', 'Sødt', 'Salt', 'Surt']:
                # Clean up common Shake-it formatting
                name = re.sub(r'\s*-\s*Shake-it.*$', '', text, flags=re.IGNORECASE)
                return name

        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Extract drink name from title like "Mojito opskrift | En simpel opskrift | shake-it.dk"
            match = re.match(r'^([^|]+)', title_text)
            if match:
                name = match.group(1).strip()
                name = re.sub(r'\s+opskrift$', '', name, flags=re.IGNORECASE)
                return name

        return "Unknown Drink"

    # Description extraction removed - too much irrelevant text

    def extract_instructions(self, soup):
        """Extract preparation instructions from Shake-it"""
        # Shake-it has numbered instructions like "1. Vælg et lavt glas"

        all_text = soup.get_text()
        lines = all_text.split('\n')

        instructions = []
        in_instructions_section = False

        for line in lines:
            line = line.strip()

            # Look for the start of instructions section
            if re.match(r'opskrift.*fremgangsmåde', line.lower()):
                in_instructions_section = True
                continue

            # Look for numbered instructions
            if re.match(r'^\d+\.\s+', line):
                in_instructions_section = True
                # Clean up the instruction
                instruction = re.sub(r'^\d+\.\s+', '', line)
                if instruction and len(instruction) > 5:
                    instructions.append(instruction)

            # Stop if we hit another section
            elif in_instructions_section and (
                line.lower().startswith(('hvilken', 'hvor', 'her er hvad', 'eksklusive')) or
                len(line) > 50 and ':' in line
            ):
                break

        if instructions:
            return ' '.join(instructions)

        # Fallback: look for any instructional text
        instruction_patterns = [
            r'(?:Fremgangsmåde|Tilberedning|Sådan gør du)[:\-\s]+(.*?)(?=\n\n|\Z)',
            r'(?:Instructions|Method)[:\-\s]+(.*?)(?=\n\n|\Z)',
        ]

        for pattern in instruction_patterns:
            match = re.search(pattern, all_text, re.DOTALL | re.IGNORECASE)
            if match:
                instructions_text = match.group(1).strip()
                # Clean up
                instructions_text = re.sub(r'\s+', ' ', instructions_text)
                return instructions_text

        return ""

    def extract_ingredients(self, soup):
        """Extract and categorize ingredients from Shake-it"""
        ingredients = []

        # Get the full text content
        full_text = soup.get_text()

        # Find the ingredient section - it's between "Antal drinks" and "Tilføj til favoritter"
        ingredient_section_pattern = r'Antal drinks\s+1(.*?)(?:Tilføj til favoritter|Isterninger)'
        match = re.search(ingredient_section_pattern, full_text, re.DOTALL)

        if match:
            ingredient_text = match.group(1)
            self.stdout.write(f"Found ingredient section: {ingredient_text[:200]}...")

            # Split into lines and clean up
            lines = [line.strip() for line in ingredient_text.split('\n') if line.strip()]

            i = 0
            while i < len(lines):
                line = lines[i]

                # Look for amount + unit pattern (like "4", "cl")
                if re.match(r'^\d+(?:[.,]\d+)?$', line) and i + 1 < len(lines):
                    # Found amount, next line should be unit
                    amount = float(line.replace(',', '.'))

                    if i + 1 < len(lines):
                        unit_line = lines[i + 1].strip()

                        # Check if next line is a unit
                        if re.match(r'^(cl|ml|stk|tsk|spsk|dråber|splash)$', unit_line, re.IGNORECASE):
                            unit = unit_line.lower()

                            # Look for ingredient name in the following lines
                            if i + 2 < len(lines):
                                ingredient_name = lines[i + 2].strip()

                                # Clean up ingredient name
                                ingredient_name = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', ingredient_name)

                                # Skip if it's not really an ingredient or should be ignored
                                if (len(ingredient_name) > 2 and
                                    not ingredient_name.lower() in ['antal drinks', 'til 1 drink', 'let at lave', 'min'] and
                                    not self.should_ignore_ingredient(ingredient_name)):

                                    parsed = {
                                        'name': ingredient_name,
                                        'amount': amount,
                                        'unit': unit,
                                        'required': self.is_ingredient_required(ingredient_name, amount)
                                    }
                                    ingredients.append(parsed)
                                    self.stdout.write(f"Found ingredient: {amount} {unit} {ingredient_name}")

                                i += 3  # Skip amount, unit, and name
                                continue

                # Look for ingredients without amounts (like "Mynteblade", "Limebåd")
                elif line and len(line) > 3:
                    # Check if it looks like an ingredient (but skip ignored ones)
                    ingredient_keywords = [
                        'mynteblade', 'limebåd', 'appelsinskive',
                        'citronskive', 'oliven', 'cocktailbær'
                    ]

                    if (any(keyword in line.lower() for keyword in ingredient_keywords) and
                        not line.lower().startswith(('opskrift', 'tilføj', 'print', 'del')) and
                        not self.should_ignore_ingredient(line)):

                        parsed = {
                            'name': line,
                            'amount': None,
                            'unit': 'efter smag',
                            'required': self.is_ingredient_required(line)
                        }
                        ingredients.append(parsed)
                        self.stdout.write(f"Found ingredient (no amount): {line}")

                i += 1

        # No longer automatically add "Isterninger" - it's not a real ingredient

        # If we still don't have enough ingredients, try a more aggressive approach
        if len(ingredients) < 2:
            self.stdout.write("Trying aggressive ingredient extraction...")

            # Look for any pattern that might be ingredients
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]

            for i, line in enumerate(lines):
                # Look for lines that contain "cl", "ml", "stk" etc.
                if re.search(r'\b\d+(?:[.,]\d+)?\s*(cl|ml|stk|tsk|spsk)\b', line, re.IGNORECASE):
                    # Try to extract amount, unit, and name from the same line
                    match = re.search(r'(\d+(?:[.,]\d+)?)\s*(cl|ml|stk|tsk|spsk)\s+(.+)', line, re.IGNORECASE)
                    if match:
                        amount = float(match.group(1).replace(',', '.'))
                        unit = match.group(2).lower()
                        name = match.group(3).strip()

                        # Clean up name
                        name = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', name)
                        name = re.sub(r'<[^>]+>', '', name)  # Remove HTML tags

                        if (len(name) > 2 and
                            not any(ing['name'] == name for ing in ingredients) and
                            not self.should_ignore_ingredient(name)):
                            parsed = {
                                'name': name,
                                'amount': amount,
                                'unit': unit,
                                'required': self.is_ingredient_required(name, amount)
                            }
                            ingredients.append(parsed)
                            self.stdout.write(f"Aggressive found: {amount} {unit} {name}")

        self.stdout.write(f"Total ingredients found: {len(ingredients)}")
        return ingredients

    def parse_ingredient(self, text):
        """Parse ingredient text into structured data"""
        # Regex til at parse mængde og enhed
        amount_pattern = r'(\d+(?:[.,]\d+)?)\s*(cl|ml|oz|stk|dråber|splash|tsk|spsk)?'
        match = re.search(amount_pattern, text, re.IGNORECASE)
        
        amount = None
        unit = None
        
        if match:
            amount = float(match.group(1).replace(',', '.'))
            unit = match.group(2) or 'stk'
            # Remove amount from ingredient name
            ingredient_name = re.sub(amount_pattern, '', text, flags=re.IGNORECASE).strip()
        else:
            ingredient_name = text
        
        # Determine if ingredient is required or optional
        is_required = self.is_ingredient_required(ingredient_name)
        
        return {
            'name': ingredient_name,
            'amount': amount,
            'unit': unit,
            'required': is_required
        }

    def should_ignore_ingredient(self, ingredient_name):
        """Check if ingredient should be completely ignored"""
        name_lower = ingredient_name.lower()

        for keyword in self.ignore_keywords:
            if keyword in name_lower:
                return True

        return False

    def is_ingredient_required(self, ingredient_name, amount=None):
        """Determine if ingredient is required based on keywords and amount"""
        name_lower = ingredient_name.lower()

        # Check if should be ignored completely
        if self.should_ignore_ingredient(ingredient_name):
            return False

        # Check amount - small amounts are usually optional
        if amount is not None:
            if amount <= 0.5:  # 0.5 cl or less = optional (like dashes, drops)
                return False
            elif amount <= 1.0 and any(word in name_lower for word in ['saft', 'juice', 'dråber', 'splash']):
                return False  # Small amounts of juice/saft are optional

        # Definitely optional (garnish, rim, decoration)
        for keyword in self.optional_keywords:
            if keyword in name_lower:
                return False

        # Definitely required (main spirits and key ingredients)
        for keyword in self.required_keywords:
            if keyword in name_lower:
                return True

        # Additional patterns for optional ingredients
        optional_patterns = [
            r'\btil\s+(pynt|rim|kant|decoration)\b',
            r'\b(pynt|garnish|decoration|twist)\b',
            r'\b(salt|sukker)\s+til\s+rim\b',
            r'\bskive\b.*\btil\s+pynt\b',
            r'\bdråber\b',
            r'\bsplash\b',
            r'\befter\s+smag\b'
        ]

        for pattern in optional_patterns:
            if re.search(pattern, name_lower):
                return False

        # Default to required for substantial ingredients
        return True

    def extract_image_url(self, soup, base_url):
        """Extract drink image URL from Shake-it"""
        # Shake-it uses imgix for images, look for the main drink image

        # Look for images with shake-it.imgix.net domain
        images = soup.find_all('img')
        for img in images:
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                # Shake-it uses imgix URLs like: https://shake-it.imgix.net/uploads/recipes/Mojito.jpg
                if 'shake-it.imgix.net' in src and 'recipes' in src:
                    return src

                # Also check for other drink-related images
                if any(keyword in src.lower() for keyword in ['drink', 'cocktail', 'recipe']):
                    if 'icon' not in src.lower() and 'logo' not in src.lower():
                        return urljoin(base_url, src)

        # Fallback: look for any substantial image
        for img in images:
            src = img.get('src') or img.get('data-src')
            if src and 'icon' not in src.lower() and 'logo' not in src.lower():
                # Check if it's likely a drink image by alt text or filename
                alt = img.get('alt', '').lower()
                if alt and any(keyword in alt for keyword in ['drink', 'cocktail', 'mojito', 'recipe']):
                    return urljoin(base_url, src)

        return None

    def determine_drink_type(self, soup):
        """Determine drink type from page content"""
        # Look for category indicators
        categories = soup.select('.category, .drink-type, .recipe-category')
        for cat in categories:
            text = cat.get_text().lower()
            if 'shot' in text:
                return 'shot'
            elif 'cocktail' in text:
                return 'cocktail'
        
        return 'drink'  # Default

    def download_image(self, image_url, drink_name):
        """Download and save drink image"""
        if not image_url:
            self.stdout.write("❌ No image URL provided")
            return None

        try:
            self.stdout.write(f"📥 Downloading image from: {image_url}")
            response = self.session.get(image_url, timeout=30)
            response.raise_for_status()

            # Check content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                self.stdout.write(f"❌ Invalid content type: {content_type}")
                return None

            # Get file extension
            parsed_url = urlparse(image_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'

            # Create filename
            safe_name = re.sub(r'[^\w\s-]', '', drink_name).strip()
            safe_name = re.sub(r'[-\s]+', '-', safe_name)
            filename = f"{safe_name}{ext}"

            self.stdout.write(f"💾 Creating file: {filename} ({len(response.content)} bytes)")

            return ContentFile(response.content, filename)

        except Exception as e:
            self.stdout.write(f"❌ Failed to download image {image_url}: {e}")
            logger.error(f"Failed to download image {image_url}: {e}")
            return None

    def save_drink(self, drink_data):
        """Save drink to database - ONLY name, type and image"""
        with transaction.atomic():
            # Create or get drink
            drink, created = Drink.objects.get_or_create(
                name=drink_data['name'],
                defaults={
                    'drink_type': drink_data['drink_type'],
                    'recipe': ''  # Empty recipe - you'll add manually
                }
            )

            if not created:
                self.stdout.write(f'⚠️  Drink {drink.name} already exists, skipping...')
                return

            # Download and save image
            if drink_data['image_url']:
                self.stdout.write(f'🔍 Attempting to download image from: {drink_data["image_url"]}')
                image_file = self.download_image(drink_data['image_url'], drink.name)
                if image_file:
                    drink.image.save(image_file.name, image_file)
                    self.stdout.write(f'📸 Image downloaded and saved for {drink.name}')
                else:
                    self.stdout.write(f'❌ Failed to download image for {drink.name}')
            else:
                self.stdout.write(f'⚠️  No image URL found for {drink.name}')

            # NO ingredients added - you handle that manually

            drink.save()
            self.stdout.write(f'✅ Created drink "{drink.name}" - ready for manual ingredient/recipe setup!')

    def print_drink_preview(self, drink_data):
        """Print what would be imported (dry run) - ONLY name and image"""
        self.stdout.write(f"\n🍹 {drink_data['name']}")
        self.stdout.write(f"🏷️  Type: {drink_data['drink_type']}")
        self.stdout.write(f"🖼️  Image: {drink_data['image_url']}")
        self.stdout.write("📝 Opskrift: [Du tilføjer selv]")
        self.stdout.write("📋 Ingredienser: [Du tilføjer selv]")
        self.stdout.write("-" * 50)

    def import_category(self, category, limit, dry_run):
        """Import multiple drinks from a category"""
        # This would need to be implemented based on Shake-it's category pages
        self.stdout.write(f"🔍 Importing {limit} drinks from category: {category}")
        # Implementation depends on Shake-it's site structure
