from django.core.management.base import BaseCommand
from django.utils.html import strip_tags
from bar.models import Drink, DrinkIngredient, Ingredient
import re

class Command(BaseCommand):
    help = 'Hjælper med at migrere eksisterende opskrifter til den nye struktur'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Vis hvad der ville blive gjort uden at ændre noget',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - ingen ændringer gemmes'))
        
        drinks = Drink.objects.all()
        
        for drink in drinks:
            self.stdout.write(f"\n🍹 Analyserer: {drink.name}")
            
            # Get plain text version of recipe
            plain_recipe = strip_tags(drink.recipe)
            
            # Try to extract ingredients with simple patterns
            # Look for patterns like "3 cl rom", "2 tsk sukker", etc.
            ingredient_patterns = [
                r'(\d+(?:\.\d+)?)\s*(cl|ml|tsk|spsk|stk|g|kg)\s+([a-zA-ZæøåÆØÅ\s]+)',
                r'(\d+(?:\.\d+)?)\s+([a-zA-ZæøåÆØÅ\s]+)\s*\((\d+(?:\.\d+)?)\s*(cl|ml|tsk|spsk|stk|g|kg)\)',
            ]
            
            found_ingredients = []
            
            for pattern in ingredient_patterns:
                matches = re.finditer(pattern, plain_recipe, re.IGNORECASE)
                for match in matches:
                    if len(match.groups()) == 3:
                        amount, unit, ingredient_name = match.groups()
                    else:
                        amount, ingredient_name, amount2, unit = match.groups()
                        amount = amount2  # Use the amount in parentheses
                    
                    ingredient_name = ingredient_name.strip()
                    found_ingredients.append({
                        'amount': float(amount),
                        'unit': unit.lower(),
                        'name': ingredient_name,
                        'original_text': match.group(0)
                    })
            
            if found_ingredients:
                self.stdout.write(f"  📋 Fundet mulige ingredienser:")
                for ing in found_ingredients:
                    self.stdout.write(f"    • {ing['amount']} {ing['unit']} {ing['name']}")
                    
                    if not dry_run:
                        # Try to find matching ingredient in database
                        try:
                            ingredient = Ingredient.objects.filter(
                                name__icontains=ing['name']
                            ).first()
                            
                            if ingredient:
                                # Create DrinkIngredient if it doesn't exist
                                drink_ing, created = DrinkIngredient.objects.get_or_create(
                                    drink=drink,
                                    ingredient=ingredient,
                                    defaults={
                                        'amount': ing['amount'],
                                        'unit': ing['unit']
                                    }
                                )
                                if created:
                                    self.stdout.write(f"      ✅ Tilføjet: {ingredient.name}")
                                else:
                                    self.stdout.write(f"      ⚠️  Findes allerede: {ingredient.name}")
                            else:
                                self.stdout.write(f"      ❌ Ingrediens ikke fundet i database: {ing['name']}")
                        except Exception as e:
                            self.stdout.write(f"      ❌ Fejl: {e}")
            else:
                self.stdout.write("  ❌ Ingen ingredienser fundet i opskriften")
            
            # Show what the new combined recipe would look like
            if not dry_run:
                combined = drink.get_full_recipe()
                self.stdout.write(f"  📖 Ny kombineret opskrift preview:")
                preview = strip_tags(combined)[:100] + "..." if len(combined) > 100 else strip_tags(combined)
                self.stdout.write(f"    {preview}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\nDette var en DRY RUN - ingen ændringer er gemt.'))
            self.stdout.write('Kør kommandoen uden --dry-run for at gemme ændringerne.')
        else:
            self.stdout.write(self.style.SUCCESS('\n✅ Migration fuldført!'))
            self.stdout.write('Gå til admin-panelet for at se og justere ingredienserne.')
