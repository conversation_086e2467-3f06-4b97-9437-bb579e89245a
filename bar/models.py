from django.db import models
from django.contrib.auth.models import User
from ckeditor.fields import RichTextField
from django.core.validators import MinValueValidator, MaxValueValidator


class Ingredient(models.Model):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to='ingredient_images/', null=True, blank=True)
    description = models.TextField(blank=True, help_text='Kort beskrivelse af ingrediensen')
    substitutes = models.ManyToManyField('self', blank=True, symmetrical=False,
                                       help_text="Ingredienser der kan erstatte denne",
                                       verbose_name="Erstatninger")

    def __str__(self):
        return self.name

    def get_all_substitutes(self):
        """Returnerer alle ingredienser der kan erstatte denne (inkl. sig selv)"""
        substitutes = list(self.substitutes.all())
        substitutes.append(self)
        return substitutes

class IngredientGroup(models.Model):
    name = models.CharField(max_length=100)
    ingredients = models.ManyToManyField(Ingredient)

    def __str__(self):
        return self.name


class DrinkIngredientGroup(models.Model):
    """Specific ingredient group amounts for each drink"""
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='drink_ingredient_groups')
    ingredient_group = models.ForeignKey(IngredientGroup, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=6, decimal_places=2, help_text="Mængde i cl/ml (f.eks. 0.75, 2.50)")
    unit = models.CharField(max_length=10, default='cl', choices=[
        ('cl', 'Centiliter'),
        ('ml', 'Milliliter'),
        ('stk', 'Stykker'),
        ('skiver', 'Skiver'),
        ('tsk', 'Teskeer'),
        ('spsk', 'Spiseskeer'),
    ])
    is_required = models.BooleanField(default=True, verbose_name="Påkrævet",
                                    help_text="Markér hvis denne ingrediens gruppe er påkrævet for drinken")

    class Meta:
        unique_together = ('drink', 'ingredient_group')

    def __str__(self):
        required_text = " (påkrævet)" if self.is_required else " (valgfri)"
        return f"{self.drink.name}: {self.amount} {self.unit} {self.ingredient_group.name}{required_text}"

class Drink(models.Model):
    DRINK_TYPES = [
        ('drink', 'Drink'),
        ('cocktail', 'Cocktail'),
        ('shot', 'Shot'),
        ('nonalcoholic', 'Alkoholfri'),
    ]

    drink_type = models.CharField(
        max_length=20,
        choices=DRINK_TYPES,
        default='drink'
    )
    name = models.CharField(max_length=100)
    required_ingredients = models.ManyToManyField(Ingredient, blank=True)
    ingredient_groups = models.ManyToManyField(IngredientGroup, blank=True)
    recipe = RichTextField(help_text="Instruktioner og ekstra noter (ingredienser tilføjes automatisk fra 'Drink ingredients' nedenfor)")
    image = models.ImageField(upload_to='drink_images/', null=True, blank=True)
    is_featured = models.BooleanField(default=False, verbose_name="Featured",
                                    help_text="Mark this drink as featured on the homepage")

    def __str__(self):
        return self.name

    def get_average_rating(self):
        avg = self.ratings.aggregate(models.Avg('stars'))['stars__avg']
        return float(avg) if avg is not None else 0.0

    def get_full_recipe(self):
        """Combines drink ingredients and ingredient groups with manual recipe instructions"""
        full_recipe = ""

        # Add ingredients section
        drink_ingredients = self.drink_ingredients.all()
        drink_ingredient_groups = self.drink_ingredient_groups.all()

        if drink_ingredients or drink_ingredient_groups:
            full_recipe += "<h6>Ingredienser:</h6><ul>"

            # Add specific ingredients
            for ingredient in drink_ingredients:
                full_recipe += f"<li>{ingredient.amount} {ingredient.unit} {ingredient.ingredient.name}</li>"

            # Add ingredient groups
            for group_item in drink_ingredient_groups:
                group_ingredients = ", ".join([ing.name for ing in group_item.ingredient_group.ingredients.all()])
                full_recipe += f"<li>{group_item.amount} {group_item.unit} {group_item.ingredient_group.name} ({group_ingredients})</li>"

            full_recipe += "</ul>"

        # Add manual recipe if exists
        if self.recipe.strip():
            if drink_ingredients or drink_ingredient_groups:
                full_recipe += "<h6>Fremgangsmåde:</h6>"
            full_recipe += self.recipe

        return full_recipe if full_recipe else self.recipe


class DrinkIngredient(models.Model):
    """Specific ingredient amounts for each drink"""
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='drink_ingredients')
    ingredient = models.ForeignKey(Ingredient, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=6, decimal_places=2, help_text="Mængde i cl/ml (f.eks. 0.75, 2.50)")
    unit = models.CharField(max_length=10, default='cl', choices=[
        ('cl', 'Centiliter'),
        ('ml', 'Milliliter'),
        ('stk', 'Stykker'),
        ('skiver', 'Skiver'),
        ('tsk', 'Teskeer'),
        ('spsk', 'Spiseskeer'),
    ])
    is_required = models.BooleanField(default=True, verbose_name="Påkrævet",
                                    help_text="Markér hvis denne ingrediens er påkrævet for drinken")

    class Meta:
        unique_together = ('drink', 'ingredient')

    def __str__(self):
        required_text = " (påkrævet)" if self.is_required else " (valgfri)"
        return f"{self.drink.name}: {self.amount} {self.unit} {self.ingredient.name}{required_text}"


# Signal to automatically update required_ingredients
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver([post_save, post_delete], sender=DrinkIngredient)
def update_drink_required_ingredients(sender, instance, **kwargs):
    """
    Når en DrinkIngredient gemmes eller slettes, opdateres
    den overordnede Drinks required_ingredients M2M-felt automatisk.
    Inkluderer både påkrævede DrinkIngredients og alle ingredienser fra ingredient groups.
    """
    drink = instance.drink
    required_ingredients = []

    # Tilføj kun påkrævede ingredienser fra DrinkIngredient
    # Ingredient groups håndteres separat i view logikken
    required_ingredients = [di.ingredient for di in drink.drink_ingredients.filter(is_required=True)]

    # Sæt M2M-feltet til kun påkrævede DrinkIngredients
    drink.required_ingredients.set(required_ingredients)


@receiver([post_save, post_delete], sender=DrinkIngredientGroup)
def update_drink_ingredient_groups(sender, instance, **kwargs):
    """
    Når en DrinkIngredientGroup gemmes eller slettes, opdater ingredient_groups M2M
    """
    drink = instance.drink
    # Få alle ingredient groups fra DrinkIngredientGroup
    ingredient_groups = [dig.ingredient_group for dig in drink.drink_ingredient_groups.all()]
    # Opdater M2M-feltet
    drink.ingredient_groups.set(ingredient_groups)

# Signal for når ingredient groups ændres på en drink
from django.db.models.signals import m2m_changed

@receiver(m2m_changed, sender=Drink.ingredient_groups.through)
def update_drink_required_ingredients_from_groups(sender, instance, action, **kwargs):
    """
    Når ingredient groups ændres på en drink, opdater required_ingredients
    """
    if action in ['post_add', 'post_remove', 'post_clear']:
        required_ingredients = []

        # Tilføj kun påkrævede ingredienser fra DrinkIngredient
        # Ingredient groups håndteres separat i view logikken
        required_ingredients = [di.ingredient for di in instance.drink_ingredients.filter(is_required=True)]

        # Sæt M2M-feltet til kun påkrævede DrinkIngredients
        instance.required_ingredients.set(required_ingredients)

class UserBar(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    ingredients = models.ManyToManyField(Ingredient, blank=True)

class Favorite(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE)

    class Meta:
        unique_together = ('user', 'drink')

    def __str__(self):
        return f"{self.user.username} ♥ {self.drink.name}"


class Rating(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='ratings')
    stars = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Antal stjerner (1-5)"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'drink') # En bruger kan kun give én bedømmelse per drink
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.drink.name}: {self.stars} stjerner"

class Comment(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at'] # Nyeste kommentarer først

    def __str__(self):
        return f"Kommentar af {self.user.username} på {self.drink.name}"


# DRINK PLANNER MODELS - Easy to remove if not wanted
class DrinkPlan(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100, help_text="Navn på din fest/event")
    guest_count = models.PositiveIntegerField(help_text="Antal gæster")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.guest_count} gæster)"


class DrinkPlanItem(models.Model):
    plan = models.ForeignKey(DrinkPlan, on_delete=models.CASCADE, related_name='items')
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE)
    servings_per_guest = models.DecimalField(max_digits=3, decimal_places=1, default=1.0,
                                           help_text="Hvor mange af denne drink per gæst")

    def total_servings(self):
        return int(self.servings_per_guest * self.plan.guest_count)

    def __str__(self):
        return f"{self.drink.name} for {self.plan.name}"