{% extends 'bar/base.html' %}

{% block title %}Rediger Min Bar{% endblock %}

{% block content %}

<!-- Epic Hero Section with Particles & Modern Animations -->
<div class="bar-hero" data-aos="fade-down" data-aos-duration="800">
    <!-- Interactive Particles Background -->
    <div id="particles-js" class="particles-background"></div>

    <div class="hero-background">
        <div class="floating-bottles">
            <div class="bottle bottle-1 animate__animated animate__fadeInUp animate__delay-1s">🍾</div>
            <div class="bottle bottle-2 animate__animated animate__fadeInUp animate__delay-2s">🍸</div>
            <div class="bottle bottle-3 animate__animated animate__fadeInUp animate__delay-3s">🥃</div>
            <div class="bottle bottle-4 animate__animated animate__fadeInUp animate__delay-4s">🍹</div>
            <div class="bottle bottle-5 animate__animated animate__fadeInUp animate__delay-5s">🍷</div>
        </div>
    </div>
    <div class="hero-content">
        <h1 class="hero-title animate__animated animate__bounceInDown">🥃 Min Personlige Bar</h1>
        <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="300">Byg din drømme-bar og opdag nye cocktail muligheder</p>
        <div class="hero-stats" data-aos="fade-up" data-aos-delay="600">
            <div class="stat-card enhanced" data-aos="zoom-in" data-aos-delay="700">
                <div class="stat-icon">🧪</div>
                <div class="stat-number" id="totalIngredients">{{ ingredients|length }}</div>
                <div class="stat-label">Ingredienser</div>
                <div class="stat-trend">📈 +3 denne uge</div>
            </div>
            <div class="stat-card enhanced" data-aos="zoom-in" data-aos-delay="800">
                <div class="stat-icon">✅</div>
                <div class="stat-number" id="selectedIngredients">{{ selected_ids|length }}</div>
                <div class="stat-label">Valgte</div>
                <div class="stat-trend" id="selectedTrend">🔥 Hot bar!</div>
            </div>
            <div class="stat-card enhanced" data-aos="zoom-in" data-aos-delay="900">
                <div class="stat-icon">🍹</div>
                <div class="stat-number" id="possibleDrinks">0</div>
                <div class="stat-label">Mulige Drinks</div>
                <div class="stat-trend" id="drinksTrend">⚡ Klar til fest!</div>
            </div>
            <div class="stat-card enhanced" data-aos="zoom-in" data-aos-delay="1000">
                <div class="stat-icon">📊</div>
                <div class="stat-number" id="completionRate">0%</div>
                <div class="stat-label">Bar Score</div>
                <div class="stat-trend" id="scoreTrend">🎯 Næsten der!</div>
            </div>
        </div>
        <div class="hero-progress" data-aos="fade-up" data-aos-delay="1100">
            <div class="progress-label">Din Bar Fremgang</div>
            <div class="progress-container">
                <div class="progress-bar" id="heroProgressBar"></div>
                <div class="progress-text" id="heroProgressText">0%</div>
            </div>
            <!-- Modern Chart.js Progress Chart -->
            <div class="chart-container" style="position: relative; height: 100px; margin-top: 20px;">
                <canvas id="progressChart"></canvas>
            </div>
        </div>
    </div>
</div>

<style>
/* Epic Hero Section - Compact */
.bar-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    padding: 60px 0 40px 0;
    margin: -20px -15px 30px -15px;
    position: relative;
    overflow: hidden;
    min-height: 35vh;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-bottles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bottle {
    position: absolute;
    font-size: 3rem;
    opacity: 0.2;
    animation: floatBottle 8s ease-in-out infinite;
}

.bottle-1 { top: 10%; left: 10%; animation-delay: 0s; }
.bottle-2 { top: 20%; right: 15%; animation-delay: 1.6s; }
.bottle-3 { top: 60%; left: 20%; animation-delay: 3.2s; }
.bottle-4 { bottom: 30%; right: 25%; animation-delay: 4.8s; }
.bottle-5 { bottom: 10%; left: 30%; animation-delay: 6.4s; }

@keyframes floatBottle {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(5deg); }
    50% { transform: translateY(-40px) rotate(-5deg); }
    75% { transform: translateY(-20px) rotate(3deg); }
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 95%;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    /* Animation handled by Animate.css */
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 40px;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    /* Animation handled by AOS */
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 5px;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-progress {
    animation: fadeInUp 1s ease-out 0.9s both;
}

.progress-label {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.progress-container {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    height: 30px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00f2fe, #4facfe, #f093fb);
    border-radius: 25px;
    transition: width 1s ease;
    width: 0%;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 800;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* fadeInUp animation replaced with AOS */

    /* General dark mode for body, already present */
    body.dark-mode {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        color: #eee; /* General text color for dark mode */
    }

    body.dark-mode .bar-hero {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #4a4a4a 100%);
    }

    /* Sticky Save Bar button container */
    .sticky-top-mobile {
        position: -webkit-sticky; /* For Safari */
        position: sticky;
        top: 0;
        z-index: 1020; /* Higher than Bootstrap's default sticky but below navbar if any */
        background-color: #f8f9fa; /* Default light background */
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 20px; /* Space below the sticky section */
    }

    body.dark-mode .sticky-top-mobile {
        background-color: #121212; /* Dark mode background for sticky section */
        border-bottom-color: #444;
    }

    /* Hide sticky on larger screens - adjust breakpoint as needed */
    @media (min-width: 768px) { /* md breakpoint */
        .sticky-top-mobile {
            position: static; /* Remove sticky on desktop */
            padding: 0;
            border-bottom: none;
            margin-bottom: 0;
        }
    }

    /* Padding for content when sticky button is active */
    @media (max-width: 767.98px) { /* sm breakpoint */
        body {
            padding-top: 65px; /* Adjust this value based on the height of your sticky button + some margin */
        }
    }

    /* Shared Column Classes - Standardized for both drinks and ingredients */
    /* Default: 8 columns for better space utilization */
    .bar-item, .grid-item {
        flex: 0 0 12.5%;
        max-width: 12.5%;
    }

    /* Mobile: 2 columns only on very small screens */
    @media (max-width: 480px) {
        .bar-item, .grid-item {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    /* Small devices (tablets, 576px and up) */
    @media (min-width: 576px) {
        .bar-item.size-4, .grid-item.size-4 { /* 4 per row */
            flex: 0 0 25%;
            max-width: 25%;
        }
        .bar-item.size-6, .grid-item.size-6 { /* 6 per row */
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .bar-item.size-8, .grid-item.size-8 { /* 8 per row */
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .bar-item.size-10, .grid-item.size-10 { /* 10 per row */
            flex: 0 0 10%;
            max-width: 10%;
        }
        .bar-item.size-12, .grid-item.size-12 { /* 12 per row */
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Medium devices (desktops, 768px and up) */
    @media (min-width: 768px) {
        .bar-item.size-4, .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .bar-item.size-6, .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .bar-item.size-8, .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .bar-item.size-10, .grid-item.size-10 {
            flex: 0 0 10%;
            max-width: 10%;
        }
        .bar-item.size-12, .grid-item.size-12 {
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Large devices (large desktops, 992px and up) */
    @media (min-width: 992px) {
        .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
    }

    /* Extra large devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
    }


    /* Epic Ingredient Cards */
    .ingredient-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 25px;
        padding: 20px;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        height: 100%;
        cursor: pointer;
        position: relative;
        color: #333;
        font-size: 0.9rem;
        overflow: hidden;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        transform: translateY(0) scale(1);
    }

    .ingredient-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 300% 100%;
        animation: gradientFlow 4s ease infinite;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .ingredient-card::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.4s ease;
        z-index: 0;
    }

    @keyframes gradientFlow {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* Epic Hover & Interaction States */
    .ingredient-card:hover {
        transform: translateY(-12px) scale(1.05);
        box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
        border-color: rgba(102, 126, 234, 0.3);
    }

    .ingredient-card:hover::before {
        opacity: 1;
    }

    .ingredient-card:hover::after {
        width: 200px;
        height: 200px;
    }

    .ingredient-card:active {
        transform: translateY(-8px) scale(1.02);
        transition: all 0.1s ease;
    }

    /* Epic Checked State */
    .ingredient-card.checked {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
        transform: translateY(-8px) scale(1.03);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25);
    }

    .ingredient-card.checked::before {
        opacity: 1;
        height: 6px;
    }

    .ingredient-card.checked::after {
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
    }

    .ingredient-card.checked:hover {
        transform: translateY(-15px) scale(1.08);
        box-shadow: 0 25px 60px rgba(102, 126, 234, 0.35);
    }

    /* Dark Mode Epic Styles */
    body.dark-mode .ingredient-card {
        background: rgba(40, 40, 60, 0.95);
        color: #e0e0e0;
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    }

    body.dark-mode .ingredient-card.checked {
        border-color: #90CAF9;
        background: linear-gradient(135deg, rgba(144, 202, 249, 0.2), rgba(206, 147, 216, 0.2));
        color: #e0e0e0;
    }

    body.dark-mode .ingredient-card:hover {
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        border-color: rgba(144, 202, 249, 0.4);
    }

    /* Epic Ingredient Images */
    .ingredient-image {
        width: 100%;
        aspect-ratio: 1 / 1;
        object-fit: contain;
        object-position: center;
        border-radius: 20px;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
        padding: 10px;
    }

    .ingredient-card:hover .ingredient-image {
        transform: scale(1.02);
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .ingredient-card.checked .ingredient-image {
        border-color: #667eea;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }

    body.dark-mode .ingredient-image {
        background: linear-gradient(135deg, #3a3a3a, #2a2a2a);
        border-color: rgba(255, 255, 255, 0.2);
    }

    body.dark-mode .ingredient-image.d-flex.align-items-center.justify-content-center.text-muted {
        color: #aaa !important;
    }

    /* Epic Ingredient Names */
    .ingredient-name {
        font-weight: 700;
        font-size: 1rem;
    }

    .info-icon {
        font-size: 0.9rem;
        color: #0d6efd; /* Blue color for light mode */
        cursor: help;
        margin-left: 4px;
        position: relative;
    }

    body.dark-mode .info-icon {
        color: #90CAF9; /* Lighter blue for dark mode */
    }

    /* Tooltip styling */
    .tooltip-container {
        position: relative;
        display: inline-block;
    }

    .tooltip-text {
        display: none;
        position: fixed;
        background-color: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 99999;
        max-width: 250px;
        white-space: normal;
        word-wrap: break-word;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        pointer-events: none;
    }

    /* Arrow pointing up */
    .tooltip-text::after {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        margin-left: -6px;
        border-width: 6px;
        border-style: solid;
        border-color: transparent transparent #333 transparent;
    }

    /* CSS hover disabled - using JavaScript instead */
    /* .tooltip-container:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
        transform: translateY(0);
    } */

    /* Modern Tippy.js Custom Theme */
    .tippy-box[data-theme~='custom'] {
        background: rgba(51, 51, 51, 0.95);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
        font-size: 0.9rem;
        line-height: 1.4;
        max-width: 280px;
    }

    .tippy-box[data-theme~='custom'] .tippy-content {
        padding: 12px 16px;
        color: white;
        text-align: left;
    }

    .tippy-box[data-theme~='custom'] .tippy-arrow {
        color: rgba(51, 51, 51, 0.95);
    }

    /* Dark mode for Tippy tooltips */
    body.dark-mode .tippy-box[data-theme~='custom'] {
        background: rgba(85, 85, 85, 0.95);
        border-color: rgba(255, 255, 255, 0.2);
    }

    body.dark-mode .tippy-box[data-theme~='custom'] .tippy-arrow {
        color: rgba(85, 85, 85, 0.95);
    }

    .form-check-input {
        display: none;
    }

/* Modern Control Dashboard - Compact */
.control-dashboard {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 15px;
    z-index: 100;
    transition: all 0.3s ease;
}

/* Mobile fixes for control dashboard */
@media (max-width: 768px) {
    .control-dashboard {
        position: relative !important;
        top: auto !important;
        margin-bottom: 15px;
        padding: 15px;
        z-index: 10;
    }

    .dashboard-stats {
        position: relative !important;
        z-index: 10;
    }
}

.control-dashboard:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.dashboard-title {
    font-size: 1.4rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    font-size: 0.8rem;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.dashboard-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.control-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

.label-icon {
    font-size: 1.2rem;
}

.search-wrapper {
    position: relative;
}

.smart-search {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-size: 1rem;
}

.smart-search:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
}

.layout-dropdown-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.layout-dropdown {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.layout-dropdown:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
}

.layout-dropdown:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.dropdown-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    pointer-events: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.layout-dropdown:focus + .dropdown-icon {
    transform: translateY(-50%) rotate(180deg);
}

/* Dark mode dropdown */
body.dark-mode .layout-dropdown {
    background: rgba(50, 50, 70, 0.9);
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .layout-dropdown:focus {
    background: rgba(60, 60, 80, 1);
    border-color: #90CAF9;
}

body.dark-mode .dropdown-icon {
    color: #90CAF9;
}

.filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-chip {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 20px;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    color: #667eea;
}

.filter-chip:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.filter-chip.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.save-section {
    grid-column: 1 / -1;
}

.mega-save-btn {
    width: 100%;
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 20px 30px;
    font-size: 1.2rem;
    font-weight: 800;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.mega-save-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(46, 204, 113, 0.3);
}

.save-ripple {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.mega-save-btn:hover .save-ripple {
    left: 100%;
}

.dashboard-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 20px;
    border-top: 2px solid rgba(102, 126, 234, 0.1);
}

.mini-stat {
    text-align: center;
}

.mini-stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 5px;
}

.mini-stat-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Dark mode for control dashboard */
body.dark-mode .control-dashboard {
    background: rgba(40, 40, 60, 0.95);
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .dashboard-title {
    background: linear-gradient(135deg, #90CAF9, #CE93D8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

body.dark-mode .control-label {
    color: #e0e0e0;
}

body.dark-mode .smart-search {
    background: rgba(50, 50, 70, 0.9);
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .filter-chip {
    background: rgba(144, 202, 249, 0.2);
    color: #90CAF9;
    border-color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .mini-stat-value {
    color: #90CAF9;
}

body.dark-mode .mini-stat-label {
    color: #aaa;
}

/* Additional Epic Animations */
@keyframes checkBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.15) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* Full Width Layout - No Wasted Space */
.container, .container-fluid {
    max-width: none !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Page content full width */
body {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Control dashboard full width */
.control-dashboard {
    margin-left: 15px;
    margin-right: 15px;
    max-width: none;
}

/* Enhanced Grid Support for High Column Counts */
@media (min-width: 1400px) {
    .bar-item.size-10, .grid-item.size-10 {
        flex: 0 0 10%;
        max-width: 10%;
    }
    .bar-item.size-12, .grid-item.size-12 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .bar-item.size-10, .grid-item.size-10 {
        flex: 0 0 12.5%;
        max-width: 12.5%;
    }
    .bar-item.size-12, .grid-item.size-12 {
        flex: 0 0 10%;
        max-width: 10%;
    }
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
    .bar-hero {
        padding: 60px 0 40px 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .dashboard-controls {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .dashboard-actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .action-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .filter-chips {
        justify-content: center;
    }

    .control-dashboard {
        margin-left: 10px;
        margin-right: 10px;
    }

    /* Force smaller grids on mobile for readability */
    .bar-item.size-10, .grid-item.size-10,
    .bar-item.size-12, .grid-item.size-12 {
        flex: 0 0 25%;
        max-width: 25%;
    }
}

/* NEW EPIC FEATURES STYLING */

/* Particles.js Background */
.particles-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Smart Search Enhancements */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.suggestion-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.suggestion-emoji {
    font-size: 1.2rem;
}

.suggestion-match mark {
    background: rgba(102, 126, 234, 0.3);
    color: #667eea;
    padding: 2px 4px;
    border-radius: 4px;
}

/* Lottie Animation Container */
.lottie-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 10000;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Enhanced Save Button */
.btn-success {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
}

.btn-loader {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

/* Enhanced Stat Cards */
.stat-card.enhanced {
    position: relative;
    overflow: hidden;
}

.stat-trend {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 4px;
    font-weight: 500;
}

/* Smart Recommendations */
.smart-recommendations {
    margin: 1rem 0;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.smart-recommendations.compact {
    margin: 0.5rem 0;
    padding: 1rem;
}

.smart-recommendations.collapsed .recommendation-cards {
    display: none;
}

.recommendations-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
}

.collapse-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    color: white;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.3s ease;
}

.collapse-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.collapse-icon {
    font-size: 1.2rem;
    font-weight: bold;
}

.title-icon {
    font-size: 2rem;
}

.section-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.recommendation-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.recommendation-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 1.5rem;
}

.card-header h4 {
    color: white;
    font-weight: 600;
    margin: 0;
}

.card-content {
    margin-bottom: 1.5rem;
}

.trending-ingredient {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ingredient-name {
    color: white;
    font-weight: 500;
}

.trend-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.impact-suggestion {
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.suggestion-text {
    color: white;
    margin-bottom: 0.5rem;
}

.impact-score {
    color: #4ecdc4;
    font-weight: 600;
    font-size: 0.9rem;
}

.discovery-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.discovery-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.discovery-value {
    color: white;
    font-weight: 500;
}

.card-action {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.card-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

</style>

<!-- Modern Control Dashboard -->
<div class="control-dashboard" data-aos="fade-up" data-aos-delay="200">
    <div class="dashboard-header">
        <h2 class="dashboard-title">🎛️ Kontrol Center</h2>
        <div class="dashboard-actions">
            <button class="action-btn" onclick="selectAllIngredients()" title="Vælg alle">
                <span class="btn-icon">✅</span>
                <span class="btn-text">Alle</span>
            </button>
            <button class="action-btn" onclick="clearAllIngredients()" title="Ryd alle">
                <span class="btn-icon">❌</span>
                <span class="btn-text">Ryd</span>
            </button>
            <button class="action-btn" onclick="randomSelection()" title="Tilfældig">
                <span class="btn-icon">🎲</span>
                <span class="btn-text">Random</span>
            </button>
        </div>
    </div>

    <div class="dashboard-controls">
        <div class="control-group">
            <label class="control-label">
                <span class="label-icon">🔍</span>
                Søg Ingredienser
            </label>
            <div class="search-wrapper">
                <input type="text"
                       class="smart-search"
                       placeholder="Søg efter ingredienser..."
                       id="searchInput">
            </div>
        </div>

        <div class="control-group">
            <label class="control-label">
                <span class="label-icon">📐</span>
                Layout
            </label>
            <div class="layout-dropdown-wrapper">
                <select id="layoutDropdown" class="layout-dropdown">
                    <option value="4">4 per række</option>
                    <option value="6">6 per række</option>
                    <option value="8" selected>8 per række</option>
                    <option value="10">10 per række</option>
                    <option value="12">12 per række</option>
                </select>
                <div class="dropdown-icon">📐</div>
            </div>
        </div>

        <div class="control-group">
            <label class="control-label">
                <span class="label-icon">🏷️</span>
                Filtrering
            </label>
            <div class="filter-chips">
                <button class="filter-chip active" data-filter="all">
                    <span class="chip-icon">🌟</span>
                    Alle
                </button>
                <button class="filter-chip" data-filter="selected">
                    <span class="chip-icon">✅</span>
                    Valgte
                </button>
                <button class="filter-chip" data-filter="unselected">
                    <span class="chip-icon">⭕</span>
                    Ikke valgte
                </button>
                <button class="filter-chip" data-filter="spirits">
                    <span class="chip-icon">🥃</span>
                    Spiritus
                </button>
                <button class="filter-chip" data-filter="mixers">
                    <span class="chip-icon">🥤</span>
                    Mixere
                </button>
            </div>
        </div>

        <div class="control-group save-section">
            <button type="submit" form="bar-form" class="mega-save-btn">
                <span class="save-icon">💾</span>
                <span class="save-text">Gem Min Bar</span>
                <div class="save-ripple"></div>
            </button>
        </div>
    </div>

    <div class="dashboard-stats">
        <div class="mini-stat">
            <span class="mini-stat-value" id="filteredCount">{{ ingredients|length }}</span>
            <span class="mini-stat-label">Vises</span>
        </div>
        <div class="mini-stat">
            <span class="mini-stat-value" id="selectedCount">{{ selected_ids|length }}</span>
            <span class="mini-stat-label">Valgte</span>
        </div>
        <div class="mini-stat">
            <span class="mini-stat-value" id="progressPercent">0%</span>
            <span class="mini-stat-label">Komplet</span>
        </div>
    </div>
</div>

<!-- Smart Recommendations Section - Compact -->
<div class="smart-recommendations compact" data-aos="fade-up">
    <div class="recommendations-header">
        <h3 class="section-title">
            <span class="title-icon">🧠</span>
            Smart Anbefalinger
            <button class="collapse-btn" onclick="toggleSection('recommendations')">
                <span class="collapse-icon">−</span>
            </button>
        </h3>
    </div>

    <div class="recommendation-cards">
        <div class="recommendation-card trending" data-aos="slide-up" data-aos-delay="100">
            <div class="card-header">
                <div class="card-icon">🔥</div>
                <h4>Trending Nu</h4>
            </div>
            <div class="card-content">
                <div class="trending-ingredient">
                    <span class="ingredient-name">Aperol</span>
                    <span class="trend-badge">+47% denne uge</span>
                </div>
                <div class="trending-ingredient">
                    <span class="ingredient-name">Prosecco</span>
                    <span class="trend-badge">+32% denne uge</span>
                </div>
                <div class="trending-ingredient">
                    <span class="ingredient-name">Elderflower Likør</span>
                    <span class="trend-badge">+28% denne uge</span>
                </div>
            </div>
            <button class="card-action">Se Alle Trends</button>
        </div>

        <div class="recommendation-card impact" data-aos="slide-up" data-aos-delay="200">
            <div class="card-header">
                <div class="card-icon">⚡</div>
                <h4>Maksimal Impact</h4>
            </div>
            <div class="card-content">
                <div class="impact-suggestion">
                    <div class="suggestion-text">
                        Tilføj <strong>Triple Sec</strong> og få <strong>12 nye drinks</strong>
                    </div>
                    <div class="impact-score">Impact Score: 92/100</div>
                </div>
                <div class="impact-suggestion">
                    <div class="suggestion-text">
                        Tilføj <strong>Angostura Bitter</strong> og få <strong>8 nye drinks</strong>
                    </div>
                    <div class="impact-score">Impact Score: 78/100</div>
                </div>
            </div>
            <button class="card-action">Se Alle Forslag</button>
        </div>

        <div class="recommendation-card discovery" data-aos="slide-up" data-aos-delay="300">
            <div class="card-header">
                <div class="card-icon">🎯</div>
                <h4>Personlig Discovery</h4>
            </div>
            <div class="card-content">
                <div class="discovery-item">
                    <span class="discovery-label">Baseret på din bar:</span>
                    <span class="discovery-value">Du elsker Gin-baserede drinks</span>
                </div>
                <div class="discovery-item">
                    <span class="discovery-label">Anbefalet kategori:</span>
                    <span class="discovery-value">Klassiske Cocktails</span>
                </div>
                <div class="discovery-item">
                    <span class="discovery-label">Næste niveau:</span>
                    <span class="discovery-value">Premium Mixere</span>
                </div>
            </div>
            <button class="card-action">Udforsk Mere</button>
        </div>
    </div>
</div>

<form method="post" id="bar-form">
    {% csrf_token %}
    <div class="row gy-4" id="ingredient-list">
        {% for ingredient in ingredients %}
            <div class="mb-4 bar-item grid-item" data-aos="fade-up" data-aos-delay="{{ forloop.counter0|add:100 }}"> <label class="ingredient-card d-block {% if ingredient.id in selected_ids %}checked{% endif %}">
                    {% if ingredient.image %}
                        <img src="{{ ingredient.image.url }}" alt="{{ ingredient.name }}" class="ingredient-image">
                    {% else %}
                        <div class="ingredient-image d-flex align-items-center justify-content-center text-muted">
                            Ingen billede
                        </div>
                    {% endif %}
                    <div class="ingredient-name">
                        {{ ingredient.name }}
                        {% if ingredient.description %}
                            <span class="tooltip-container">
                                <span class="info-icon">❓</span>
                                <span class="tooltip-text">{{ ingredient.description }}</span>
                            </span>
                        {% endif %}
                    </div>
                    <input class="form-check-input" type="checkbox" name="ingredients" value="{{ ingredient.id }}"
                           {% if ingredient.id in selected_ids %}checked{% endif %}>
                </label>
            </div>
        {% endfor %}
    </div>
    <button type="submit" class="btn btn-success mt-4 d-none d-md-block">
        <span class="btn-text">Gem min bar</span>
        <span class="btn-loader" style="display: none;">💾 Gemmer...</span>
    </button>
</form>

<!-- Lottie Success Animation Container -->
<div id="lottie-success" class="lottie-container" style="display: none;"></div>

<a href="{% url 'drink_list' %}" class="btn btn-link mt-4">Se hvad jeg kan lave</a>

<script>
    document.querySelectorAll('.ingredient-card input[type="checkbox"]').forEach(cb => {
        cb.addEventListener('change', function () {
            const card = this.closest('.ingredient-card');
            card.classList.toggle('checked', this.checked);
        });
    });

    document.getElementById('searchInput').addEventListener('input', function () {
        const query = this.value.toLowerCase();
        const items = document.querySelectorAll('.bar-item');
        items.forEach(item => {
            const name = item.querySelector('.ingredient-name').textContent.toLowerCase();
            item.style.display = name.includes(query) ? '' : 'none';
        });
    });

    // Old barGridSizeSelect code removed - now handled in window.onload

    function updateBarGridSize(size) {
        // Apply to all ingredient items
        const barItems = document.querySelectorAll('.bar-item');

        barItems.forEach((item) => {
            item.classList.remove('size-4', 'size-6', 'size-8', 'size-10', 'size-12');
            item.classList.add('size-' + size);
        });

        // Update dropdown selection
        const layoutDropdown = document.getElementById('layoutDropdown');
        if (layoutDropdown) {
            layoutDropdown.value = size;
        }
    }

    // Old code removed - now handled in window.onload

    // Epic New Functions for Modern Bar Interface

    // Select all ingredients
    function selectAllIngredients() {
        const checkboxes = document.querySelectorAll('.ingredient-card input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = true;
            cb.closest('.ingredient-card').classList.add('checked');
        });
        updateStats();
        showNotification('✅ Alle ingredienser valgt!', 'success');
    }

    // Clear all ingredients
    function clearAllIngredients() {
        const checkboxes = document.querySelectorAll('.ingredient-card input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.closest('.ingredient-card').classList.remove('checked');
        });
        updateStats();
        showNotification('❌ Alle ingredienser fravalgt!', 'info');
    }

    // Random selection
    function randomSelection() {
        const checkboxes = document.querySelectorAll('.ingredient-card input[type="checkbox"]');
        const randomCount = Math.floor(Math.random() * checkboxes.length * 0.3) + 5; // 5-30% random

        // Clear all first
        clearAllIngredients();

        // Select random ones
        const shuffled = Array.from(checkboxes).sort(() => 0.5 - Math.random());
        shuffled.slice(0, randomCount).forEach(cb => {
            cb.checked = true;
            cb.closest('.ingredient-card').classList.add('checked');
        });

        updateStats();
        showNotification(`🎲 ${randomCount} tilfældige ingredienser valgt!`, 'success');
    }

    // Update statistics
    function updateStats() {
        const total = document.querySelectorAll('.ingredient-card').length;
        const selected = document.querySelectorAll('.ingredient-card.checked').length;
        const percentage = Math.round((selected / total) * 100);

        // Update hero stats
        document.getElementById('selectedIngredients').textContent = selected;
        document.getElementById('possibleDrinks').textContent = Math.floor(selected / 3); // Rough estimate
        document.getElementById('completionRate').textContent = percentage + '%';

        // Update dashboard stats
        document.getElementById('selectedCount').textContent = selected;
        document.getElementById('progressPercent').textContent = percentage + '%';

        // Update progress bars
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            bar.style.width = percentage + '%';
        });

        document.getElementById('heroProgressText').textContent = percentage + '%';
    }

    // Layout dropdown switching - moved to window.onload

    // Filter functionality
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.addEventListener('click', function() {
            document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            const filter = this.dataset.filter;
            filterIngredients(filter);

            showNotification(`🏷️ Filter: ${this.textContent}`, 'info');
        });
    });

    function filterIngredients(filter) {
        const cards = document.querySelectorAll('.bar-item');
        let visibleCount = 0;

        cards.forEach(card => {
            const ingredientCard = card.querySelector('.ingredient-card');
            const isChecked = ingredientCard.classList.contains('checked');
            let show = true;

            switch(filter) {
                case 'selected':
                    show = isChecked;
                    break;
                case 'unselected':
                    show = !isChecked;
                    break;
                case 'spirits':
                    // You can add logic to detect spirits based on ingredient name/type
                    show = card.textContent.toLowerCase().includes('vodka') ||
                           card.textContent.toLowerCase().includes('whisky') ||
                           card.textContent.toLowerCase().includes('gin') ||
                           card.textContent.toLowerCase().includes('rum');
                    break;
                case 'mixers':
                    show = card.textContent.toLowerCase().includes('juice') ||
                           card.textContent.toLowerCase().includes('soda') ||
                           card.textContent.toLowerCase().includes('tonic');
                    break;
                case 'all':
                default:
                    show = true;
                    break;
            }

            card.style.display = show ? 'block' : 'none';
            if (show) visibleCount++;
        });

        document.getElementById('filteredCount').textContent = visibleCount;
    }

    // Modern notifications with SweetAlert2
    function showNotification(message, type = 'info') {
        const config = {
            title: type === 'success' ? 'Succes!' : type === 'error' ? 'Fejl!' : 'Info',
            text: message,
            icon: type,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            background: 'rgba(255, 255, 255, 0.95)',
            backdrop: false,
            customClass: {
                popup: 'modern-toast'
            }
        };

        Swal.fire(config);
    }

    // Enhanced checkbox handling
    document.querySelectorAll('.ingredient-card input[type="checkbox"]').forEach(cb => {
        cb.addEventListener('change', function () {
            const card = this.closest('.ingredient-card');
            card.classList.toggle('checked', this.checked);

            // Add bounce animation
            card.style.animation = 'checkBounce 0.5s ease';
            setTimeout(() => {
                card.style.animation = '';
            }, 500);

            updateStats();
        });
    });

    // Use DOMContentLoaded instead of window.onload for faster execution
    document.addEventListener('DOMContentLoaded', function() {
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }

        // Initialize stats
        updateStats();

        // Set default grid size to 8 if not set
        const savedBarSize = localStorage.getItem('barGridSize') || '8';
        updateBarGridSize(savedBarSize);

        // Setup layout dropdown switching
        const layoutDropdown = document.getElementById('layoutDropdown');

        if (layoutDropdown) {
            layoutDropdown.addEventListener('change', function() {
                const size = this.value;
                updateBarGridSize(size);
                localStorage.setItem('barGridSize', size);

                showNotification(`📐 Layout ændret til ${size} per række`, 'info');
            });
        }

        // Initialize AOS (Animate On Scroll) - with safety check
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 600,
                easing: 'ease-in-out',
                once: true,
                offset: 50
            });
        } else {
            console.log('AOS not loaded, skipping scroll animations');
        }

        // Initialize modern features with safety checks
        try {
            initGSAPAnimations();
        } catch (e) {
            console.log('GSAP animations failed:', e);
        }

        try {
            initProgressChart();
        } catch (e) {
            console.log('Progress chart failed:', e);
        }

        try {
            initMasonryLayout();
        } catch (e) {
            console.log('Masonry layout failed:', e);
        }

        try {
            initParticles();
        } catch (e) {
            console.log('Particles failed:', e);
        }

        try {
            initScrollAnimations();
        } catch (e) {
            console.log('Scroll animations failed:', e);
        }

        // Initialize modern tooltips with safety check
        try {
            initializeTooltips();
        } catch (e) {
            console.log('Tooltip initialization failed:', e);
        }

        // Initialize simple search
        try {
            initSimpleSearch();
        } catch (e) {
            console.log('Search initialization failed:', e);
        }
    });

    // This function should ideally be in base.html or a global JS file
    // if it's used across multiple pages for dark mode toggle.
    function toggleDarkMode() {
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
    }

    // Collapse/expand functionality for sections
    function toggleSection(sectionType) {
        const section = document.querySelector(`.${sectionType === 'recommendations' ? 'smart-recommendations' : 'drink-discovery'}`);
        const icon = section.querySelector('.collapse-icon');

        section.classList.toggle('collapsed');

        if (section.classList.contains('collapsed')) {
            icon.textContent = '+';
        } else {
            icon.textContent = '−';
        }
    }

    // Adjust padding-top on body when sticky button is visible
    // This is a simple way, more robust solutions might use ResizeObserver or specific libraries.
    function adjustBodyPadding() {
        const stickyElement = document.querySelector('.sticky-top-mobile');
        if (stickyElement && window.innerWidth < 768) { // Only on smaller screens
            document.body.style.paddingTop = stickyElement.offsetHeight + 'px';
        } else {
            document.body.style.paddingTop = '0'; // Reset on larger screens
        }
    }

    // Run on load and resize
    window.addEventListener('load', adjustBodyPadding);
    window.addEventListener('resize', adjustBodyPadding);

    // Optimized Tooltip with Lazy Loading
    function initializeTooltips() {
        const tooltipContainers = document.querySelectorAll('.tooltip-container');
        console.log('🎯 Setting up lazy tooltips for', tooltipContainers.length, 'elements');

        // Lazy initialization - only create tooltip when first hovered
        tooltipContainers.forEach((container, index) => {
            const tooltipText = container.querySelector('.tooltip-text');
            if (!tooltipText) return;

            const content = tooltipText.textContent || tooltipText.innerHTML;
            let tippyInstance = null;

            // Create tooltip only on first hover
            container.addEventListener('mouseenter', function initTooltip() {
                if (!tippyInstance) {
                    tippyInstance = tippy(container, {
                        content: content,
                        theme: 'custom',
                        placement: 'bottom',
                        arrow: true,
                        animation: 'shift-away',
                        duration: [150, 100],
                        delay: [50, 25],
                        maxWidth: 280,
                        zIndex: 99999,
                        appendTo: document.body
                    });

                    // Remove event listener after first use
                    container.removeEventListener('mouseenter', initTooltip);

                    // Trigger show immediately
                    tippyInstance.show();
                }
            });

            // Hide old tooltip element
            tooltipText.style.display = 'none';
        });

        console.log('🚀 Lazy tooltips ready!');
    }

    // Advanced GSAP animations for premium feel
    function initGSAPAnimations() {
        // Floating bottles with GSAP
        gsap.to(".bottle", {
            y: -20,
            rotation: 5,
            duration: 2,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1,
            stagger: 0.3
        });

        // Progress bar animation
        const progressBar = document.getElementById('heroProgressBar');
        if (progressBar) {
            gsap.fromTo(progressBar,
                { width: '0%' },
                {
                    width: progressBar.style.width || '0%',
                    duration: 2,
                    ease: "power2.out",
                    delay: 1
                }
            );
        }

        // Stat numbers count-up animation
        document.querySelectorAll('.stat-number').forEach(stat => {
            const finalValue = parseInt(stat.textContent) || 0;
            gsap.fromTo(stat,
                { textContent: 0 },
                {
                    textContent: finalValue,
                    duration: 2,
                    ease: "power2.out",
                    snap: { textContent: 1 },
                    delay: 0.5
                }
            );
        });
    }

    // Modern Chart.js progress visualization
    function initProgressChart() {
        // Check if Chart.js is loaded
        if (typeof Chart === 'undefined') {
            console.log('Chart.js not loaded, skipping progress chart');
            return;
        }

        const ctx = document.getElementById('progressChart');
        if (!ctx) {
            console.log('Progress chart canvas not found, skipping chart');
            return;
        }

        const selectedCount = document.querySelectorAll('.ingredient-card input:checked').length;
        const totalCount = document.querySelectorAll('.ingredient-card input').length;
        const percentage = totalCount > 0 ? Math.round((selectedCount / totalCount) * 100) : 0;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [percentage, 100 - percentage],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(255, 255, 255, 0.2)'
                    ],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            },
            plugins: [{
                beforeDraw: function(chart) {
                    const width = chart.width;
                    const height = chart.height;
                    const ctx = chart.ctx;

                    ctx.restore();
                    const fontSize = (height / 114).toFixed(2);
                    ctx.font = fontSize + "em sans-serif";
                    ctx.textBaseline = "middle";
                    ctx.fillStyle = "white";

                    const text = percentage + "%";
                    const textX = Math.round((width - ctx.measureText(text).width) / 2);
                    const textY = height / 2;

                    ctx.fillText(text, textX, textY);
                    ctx.save();
                }
            }]
        });
    }

    // Masonry layout for optimal card arrangement
    function initMasonryLayout() {
        // Check if Masonry is loaded
        if (typeof Masonry === 'undefined') {
            console.log('Masonry not loaded, skipping masonry layout');
            return;
        }

        const grid = document.getElementById('ingredient-list');
        if (!grid) {
            console.log('Ingredient list not found, skipping masonry');
            return;
        }

        // Initialize Masonry after images load
        const masonry = new Masonry(grid, {
            itemSelector: '.bar-item',
            columnWidth: '.bar-item',
            percentPosition: true,
            gutter: 20,
            transitionDuration: '0.3s'
        });

        // Relayout when images load
        grid.addEventListener('load', () => masonry.layout(), true);

        // Update layout when grid size changes
        window.addEventListener('resize', () => masonry.layout());
    }

    // Interactive Particles Background
    function initParticles() {
        // Check if particles.js is loaded and element exists
        if (typeof particlesJS === 'undefined') {
            console.log('Particles.js not loaded, skipping particles');
            return;
        }

        const particlesContainer = document.getElementById('particles-js');
        if (!particlesContainer) {
            console.log('Particles container not found, skipping particles');
            return;
        }

        particlesJS('particles-js', {
            particles: {
                number: { value: 50, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.1, random: true },
                size: { value: 3, random: true },
                line_linked: { enable: true, distance: 150, color: "#ffffff", opacity: 0.1, width: 1 },
                move: { enable: true, speed: 1, direction: "none", random: true, out_mode: "out" }
            },
            interactivity: {
                detect_on: "canvas",
                events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" } },
                modes: { repulse: { distance: 100, duration: 0.4 }, push: { particles_nb: 4 } }
            },
            retina_detect: true
        });
    }

    // GSAP ScrollTrigger Animations
    function initScrollAnimations() {
        // Check if GSAP and ScrollTrigger are available
        if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
            console.log('GSAP or ScrollTrigger not loaded, skipping scroll animations');
            return;
        }

        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        // Check if ingredient cards exist
        const cards = document.querySelectorAll(".ingredient-card");
        if (cards.length === 0) {
            console.log('No ingredient cards found, skipping scroll animations');
            return;
        }

        gsap.fromTo(".ingredient-card",
            { y: 50, opacity: 0 },
            {
                y: 0,
                opacity: 1,
                duration: 0.6,
                stagger: 0.1,
                scrollTrigger: {
                    trigger: "#ingredient-list",
                    start: "top 80%",
                    end: "bottom 20%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    }

    // Simple search functionality
    function initSimpleSearch() {
        const searchInput = document.getElementById('searchInput');

        if (!searchInput) {
            console.log('Search input not found, skipping search');
            return;
        }

        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const ingredients = document.querySelectorAll('.bar-item');

            ingredients.forEach(item => {
                const ingredientName = item.querySelector('.ingredient-name');
                if (ingredientName) {
                    const name = ingredientName.textContent.toLowerCase();
                    if (name.includes(query)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        });
    }

    // Lottie Success Animation
    function showLottieAnimation() {
        // Check if Lottie is loaded
        if (typeof lottie === 'undefined') {
            console.log('Lottie not loaded, skipping animation');
            return;
        }

        const container = document.getElementById('lottie-success');
        if (!container) {
            console.log('Lottie container not found');
            return;
        }

        container.style.display = 'block';

        // Load and play success animation
        const animation = lottie.loadAnimation({
            container: container,
            renderer: 'svg',
            loop: false,
            autoplay: true,
            path: 'https://assets2.lottiefiles.com/packages/lf20_jbrw3hcz.json' // Success checkmark
        });

        // Hide after animation
        setTimeout(() => {
            container.style.display = 'none';
            animation.destroy();
        }, 3000);
    }

    // Enhanced Save Button with Loading State
    function showSaveAnimation(event) {
        // Prevent default form submission for demo
        if (event) event.preventDefault();

        const btn = event ? event.target : document.querySelector('.btn-success');
        if (!btn) return;

        const btnText = btn.querySelector('.btn-text');
        const btnLoader = btn.querySelector('.btn-loader');

        if (!btnText || !btnLoader) {
            console.log('Button elements not found, using simple notification');
            showNotification('Bar gemt! 🍾', 'success');
            return;
        }

        // Show loading state
        btnText.style.display = 'none';
        btnLoader.style.display = 'inline';
        btn.disabled = true;

        // Simulate save process
        setTimeout(() => {
            // Show Lottie success animation if available
            showLottieAnimation();

            // Reset button
            btnText.style.display = 'inline';
            btnLoader.style.display = 'none';
            btn.disabled = false;

            // Show success notification
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Succes!',
                    text: 'Din bar er gemt! 🍾',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                showNotification('Bar gemt! 🍾', 'success');
            }
        }, 1000);
    }

</script>

{% endblock %}