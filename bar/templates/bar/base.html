{% load static %}
<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Min Bar{% endblock %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#667eea">
    <meta name="description" content="Administrer din hjemmebar og find drinks du kan lave">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% static 'manifest.json' %}">

    <!-- App Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'icons/icon-192x192.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'icons/icon-192x192.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'icons/icon-192x192.png' %}"
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Modern Libraries Stack -->
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Advanced Libraries for Enhanced UX -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/lottie-web@5.12.2/build/player/lottie.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
    <script src="https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>

    <!-- Custom Styling for Modern Libraries -->
    <style>
        /* SweetAlert2 Custom Styling */
        .modern-toast {
            backdrop-filter: blur(15px) !important;
            border-radius: 15px !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2) !important;
        }

        .swal2-popup {
            border-radius: 20px !important;
            backdrop-filter: blur(15px) !important;
        }

        /* Chart.js Custom Styling */
        .chart-container {
            filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.1));
        }

        /* Masonry Layout Enhancements */
        .masonry-item {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .masonry-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
    </style>
    <style>
        body { padding-top: 4rem; }
        .navbar-brand { font-weight: bold; }
        body.dark-mode {
            background-color: #121212;
            color: #eee;
        }
        body.dark-mode .navbar,
        body.dark-mode .card,
        body.dark-mode .modal-box {
            background-color: #1f1f1f;
            color: #eee;
        }
    </style>

    <!-- PWA & Mobile Styles -->
    <style>
        /* Mobile Bottom Navigation */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
        }

        .mobile-bottom-nav .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .mobile-bottom-nav .nav-item:hover,
        .mobile-bottom-nav .nav-item.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .mobile-bottom-nav .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 2px;
        }

        .mobile-bottom-nav .nav-label {
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* PWA Install Prompt */
        .install-prompt {
            position: fixed;
            bottom: 80px;
            left: 16px;
            right: 16px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .install-prompt.show {
            transform: translateY(0);
            opacity: 1;
        }

        .install-content {
            display: flex;
            align-items: center;
            padding: 16px;
            gap: 12px;
        }

        .install-icon {
            font-size: 2rem;
            flex-shrink: 0;
        }

        .install-text {
            flex: 1;
        }

        .install-text h6 {
            margin: 0 0 4px 0;
            font-weight: 600;
        }

        .install-text p {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            body {
                padding-bottom: 80px !important; /* Space for bottom nav */
                padding-top: 1rem !important; /* Less top padding */
            }

            .container {
                padding-left: 12px;
                padding-right: 12px;
            }

            /* Hide desktop navbar on mobile */
            .navbar {
                display: none !important;
            }

            /* Larger touch targets */
            .btn {
                min-height: 44px;
                padding: 12px 20px;
                font-size: 16px;
            }

            /* Better form inputs */
            .form-control,
            .form-select {
                min-height: 44px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            /* Card optimizations */
            .card {
                border-radius: 16px;
                margin-bottom: 16px;
            }
        }

        /* Dark mode for mobile nav */
        body.dark-mode .mobile-bottom-nav {
            background: rgba(33, 37, 41, 0.95);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .mobile-bottom-nav .nav-item {
            color: #adb5bd;
        }

        body.dark-mode .mobile-bottom-nav .nav-item:hover,
        body.dark-mode .mobile-bottom-nav .nav-item.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.2);
        }

        body.dark-mode .install-prompt {
            background: #2d3748;
            color: white;
        }

        /* Toast Notifications */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1002;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-success { background: #28a745; }
        .toast-info { background: #17a2b8; }
        .toast-warning { background: #ffc107; color: #333; }
        .toast-error { background: #dc3545; }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{% url 'drink_list' %}">Min Bar</a>

        <!-- Burger menu knap til mobil -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarMenu">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'drink_list' %}">Mine Drinks</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'my_bar' %}">Rediger Bar</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'drink_planner' %}">🧮 Ingrediens Beregner</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                {% if user.is_authenticated %}
                    <li class="nav-item">
                        <form method="post" action="{% url 'logout' %}" style="display:inline;">
                            {% csrf_token %}
                            <button type="submit" class="nav-link btn btn-link" style="padding: 0; margin: 0; border: none; color: inherit;">
                                Log ud ({{ user.username }})
                            </button>
                        </form>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">Log ind</a>
                    </li>
                {% endif %}
                {% if user.is_authenticated and user.is_staff %}
                    <li class="nav-item dropdown">
                        <a class="btn btn-outline-light ms-2 dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/">🔧 Django Admin</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'import_drinks' %}">🤖 Auto Import</a></li>
                        </ul>
                    </li>
                {% endif %}
                <li class="nav-item">
                    <button class="btn btn-sm btn-outline-light ms-2" onclick="toggleDarkMode()">🌙</button>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Mobile Bottom Navigation -->
<div class="mobile-bottom-nav d-md-none">
    <a href="/" class="nav-item {% if request.resolver_match.url_name == 'home' %}active{% endif %}">
        <div class="nav-icon">🏠</div>
        <div class="nav-label">Hjem</div>
    </a>
    <a href="{% url 'my_bar' %}" class="nav-item {% if request.resolver_match.url_name == 'my_bar' %}active{% endif %}">
        <div class="nav-icon">🧪</div>
        <div class="nav-label">Min Bar</div>
    </a>
    <a href="{% url 'drink_list' %}" class="nav-item {% if request.resolver_match.url_name == 'drink_list' %}active{% endif %}">
        <div class="nav-icon">🍹</div>
        <div class="nav-label">Drinks</div>
    </a>
    <a href="#" class="nav-item" onclick="togglePartyMode()">
        <div class="nav-icon">🎉</div>
        <div class="nav-label">Party</div>
    </a>
</div>

<!-- PWA Install Button -->
<div id="installPrompt" class="install-prompt d-none">
    <div class="install-content">
        <div class="install-icon">📱</div>
        <div class="install-text">
            <h6>Installer MyBar App</h6>
            <p>Få hurtig adgang fra din hjemmeskærm</p>
        </div>
        <button id="installBtn" class="btn btn-primary btn-sm">Installer</button>
        <button id="dismissBtn" class="btn btn-outline-secondary btn-sm">Senere</button>
    </div>
</div>

<div class="container">
    {% block content %}{% endblock %}
</div>

<script>
    function toggleDarkMode() {
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
    }

    window.onload = function() {
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }

        // Initialize PWA features
        initPWA();
    };

    // PWA Installation and Service Worker
    let deferredPrompt;

    function initPWA() {
        // Register service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/sw.js')
                .then(registration => {
                    console.log('✅ Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('❌ Service Worker registration failed:', error);
                });
        }

        // Handle install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });

        // Handle successful installation
        window.addEventListener('appinstalled', (evt) => {
            console.log('✅ MyBar app installed successfully');
            hideInstallPrompt();
            showToast('App installeret! 🎉', 'success');
        });
    }

    function showInstallPrompt() {
        const prompt = document.getElementById('installPrompt');
        if (prompt) {
            prompt.classList.remove('d-none');
            prompt.classList.add('show');
        }
    }

    function hideInstallPrompt() {
        const prompt = document.getElementById('installPrompt');
        if (prompt) {
            prompt.classList.add('d-none');
            prompt.classList.remove('show');
        }
    }

    // Install button click
    document.addEventListener('DOMContentLoaded', function() {
        const installBtn = document.getElementById('installBtn');
        const dismissBtn = document.getElementById('dismissBtn');

        if (installBtn) {
            installBtn.addEventListener('click', async () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    const { outcome } = await deferredPrompt.userChoice;

                    if (outcome === 'accepted') {
                        console.log('✅ User accepted install prompt');
                    } else {
                        console.log('❌ User dismissed install prompt');
                    }

                    deferredPrompt = null;
                    hideInstallPrompt();
                }
            });
        }

        if (dismissBtn) {
            dismissBtn.addEventListener('click', () => {
                hideInstallPrompt();
                localStorage.setItem('installPromptDismissed', 'true');
            });
        }
    });

    function togglePartyMode() {
        // Party mode functionality - to be implemented
        showToast('Party Mode kommer snart! 🎉', 'info');
    }

    function showToast(message, type = 'info') {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
</script>
</body>
</html>
