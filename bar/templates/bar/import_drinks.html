{% extends 'bar/base.html' %}

{% block title %}Import Drinks{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Hero Section -->
            <div class="import-hero text-center mb-5">
                <h1 class="display-4">🤖 Shake-it Import</h1>
                <p class="lead">Kopier et Shake-it link og få automatisk importeret drink med ingredienser og billede!</p>
                <div class="hero-example">
                    <small class="text-light">
                        Eksempel: https://shake-it.dk/drinks/mojito/ → Automatisk Mojito med alle ingredienser ✨
                    </small>
                </div>
            </div>

            <!-- Quick Import Card -->
            <div class="card shadow-lg mb-4" data-aos="fade-up">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">⚡ Hurtig Import</h3>
                </div>
                <div class="card-body">
                    <form id="quickImportForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="drinkUrl" class="form-label">
                                <strong>Shake-it URL:</strong>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">🔗</span>
                                <input type="url"
                                       class="form-control"
                                       id="drinkUrl"
                                       placeholder="https://shake-it.dk/drink/mojito/"
                                       required>
                                <button class="btn btn-success" type="submit" id="importBtn">
                                    <span class="btn-text">Import Nu!</span>
                                    <span class="btn-spinner d-none">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        Importerer...
                                    </span>
                                </button>
                            </div>
                            <div class="form-text">
                                Kopier URL fra Shake-it opskrift og klik "Import Nu!"
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Import Card -->
            <div class="card shadow-lg mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header bg-info text-white">
                    <h3 class="mb-0">📦 Bulk Import</h3>
                </div>
                <div class="card-body">
                    <form id="bulkImportForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <label for="category" class="form-label">Kategori:</label>
                                <select class="form-select" id="category">
                                    <option value="cocktails">🍸 Cocktails</option>
                                    <option value="shots">🔫 Shots</option>
                                    <option value="drinks">🍹 Drinks</option>
                                    <option value="non-alcoholic">🚫 Alkoholfri</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="limit" class="form-label">Antal:</label>
                                <select class="form-select" id="limit">
                                    <option value="5">5 drinks</option>
                                    <option value="10" selected>10 drinks</option>
                                    <option value="20">20 drinks</option>
                                    <option value="50">50 drinks</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-info" type="submit">
                                📦 Start Bulk Import
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="previewBtn">
                                👁️ Preview Først
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- URL List Import -->
            <div class="card shadow-lg mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0">📝 Liste Import</h3>
                </div>
                <div class="card-body">
                    <form id="listImportForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="urlList" class="form-label">
                                <strong>URL Liste (en per linje):</strong>
                            </label>
                            <textarea class="form-control" 
                                      id="urlList" 
                                      rows="6" 
                                      placeholder="https://shake-it.dk/opskrifter/mojito
https://shake-it.dk/opskrifter/margarita
https://shake-it.dk/opskrifter/cosmopolitan"></textarea>
                            <div class="form-text">
                                Indsæt flere URLs, en per linje
                            </div>
                        </div>
                        <button class="btn btn-warning" type="submit">
                            📝 Import Liste
                        </button>
                    </form>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="card shadow-lg mb-4 d-none" id="progressCard">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0">📊 Import Progress</h3>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             id="progressBar" 
                             style="width: 0%"></div>
                    </div>
                    <div id="progressText">Starter import...</div>
                    <div id="importResults" class="mt-3"></div>
                </div>
            </div>

            <!-- Recent Imports -->
            <div class="card shadow-lg" data-aos="fade-up" data-aos-delay="600">
                <div class="card-header bg-secondary text-white">
                    <h3 class="mb-0">📋 Seneste Imports</h3>
                </div>
                <div class="card-body">
                    <div id="recentImports">
                        <p class="text-muted">Ingen imports endnu...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">👁️ Import Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuller</button>
                <button type="button" class="btn btn-primary" id="confirmImport">
                    ✅ Bekræft Import
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init();
    }

    // Quick Import Form
    document.getElementById('quickImportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const url = document.getElementById('drinkUrl').value;
        if (url) {
            importSingleDrink(url);
        }
    });

    // Bulk Import Form
    document.getElementById('bulkImportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const category = document.getElementById('category').value;
        const limit = document.getElementById('limit').value;
        startBulkImport(category, limit);
    });

    // List Import Form
    document.getElementById('listImportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const urlList = document.getElementById('urlList').value;
        const urls = urlList.split('\n').filter(url => url.trim());
        if (urls.length > 0) {
            importUrlList(urls);
        }
    });

    // Preview Button
    document.getElementById('previewBtn').addEventListener('click', function() {
        const category = document.getElementById('category').value;
        const limit = document.getElementById('limit').value;
        previewImport(category, limit);
    });
});

function importSingleDrink(url) {
    showProgress();
    updateProgress(0, 'Starter import...');
    
    fetch('/api/import-drink/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({ url: url })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateProgress(100, `✅ ${data.drink_name} importeret!`);
            addToRecentImports(data.drink_name, url);
            showSuccess(`Drink "${data.drink_name}" er importeret!`);
        } else {
            showError(data.error || 'Import fejlede');
        }
    })
    .catch(error => {
        showError('Netværksfejl: ' + error.message);
    });
}

function startBulkImport(category, limit) {
    showProgress();
    updateProgress(0, `Starter bulk import af ${limit} ${category}...`);
    
    fetch('/api/bulk-import/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({ 
            category: category, 
            limit: parseInt(limit) 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateProgress(100, `✅ ${data.imported_count} drinks importeret!`);
            showSuccess(`${data.imported_count} drinks importeret fra ${category}`);
        } else {
            showError(data.error || 'Bulk import fejlede');
        }
    })
    .catch(error => {
        showError('Netværksfejl: ' + error.message);
    });
}

function importUrlList(urls) {
    showProgress();
    let completed = 0;
    const total = urls.length;
    
    updateProgress(0, `Importerer ${total} drinks...`);
    
    // Import one by one to avoid overwhelming the server
    urls.forEach((url, index) => {
        setTimeout(() => {
            fetch('/api/import-drink/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({ url: url.trim() })
            })
            .then(response => response.json())
            .then(data => {
                completed++;
                const progress = (completed / total) * 100;
                
                if (data.success) {
                    updateProgress(progress, `✅ ${completed}/${total}: ${data.drink_name}`);
                    addToRecentImports(data.drink_name, url);
                } else {
                    updateProgress(progress, `❌ ${completed}/${total}: Fejl ved ${url}`);
                }
                
                if (completed === total) {
                    showSuccess(`Import komplet! ${completed} drinks behandlet.`);
                }
            })
            .catch(error => {
                completed++;
                const progress = (completed / total) * 100;
                updateProgress(progress, `❌ ${completed}/${total}: Netværksfejl`);
            });
        }, index * 2000); // 2 second delay between requests
    });
}

function previewImport(category, limit) {
    // Show preview modal with what would be imported
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    document.getElementById('previewContent').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status"></div>
            <p class="mt-2">Henter preview...</p>
        </div>
    `;
    modal.show();
    
    fetch('/api/preview-import/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({ 
            category: category, 
            limit: parseInt(limit) 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let previewHtml = '<div class="row">';
            data.drinks.forEach(drink => {
                previewHtml += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${drink.name}</h6>
                                <p class="card-text small">${drink.description.substring(0, 100)}...</p>
                                <small class="text-muted">${drink.ingredients.length} ingredienser</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            previewHtml += '</div>';
            document.getElementById('previewContent').innerHTML = previewHtml;
        } else {
            document.getElementById('previewContent').innerHTML = `
                <div class="alert alert-danger">
                    Fejl ved preview: ${data.error}
                </div>
            `;
        }
    });
}

function showProgress() {
    document.getElementById('progressCard').classList.remove('d-none');
}

function updateProgress(percent, text) {
    document.getElementById('progressBar').style.width = percent + '%';
    document.getElementById('progressText').textContent = text;
}

function addToRecentImports(drinkName, url) {
    const recentDiv = document.getElementById('recentImports');
    const importItem = document.createElement('div');
    importItem.className = 'alert alert-success alert-dismissible fade show';
    importItem.innerHTML = `
        <strong>${drinkName}</strong> importeret fra 
        <a href="${url}" target="_blank" class="alert-link">Shake-it</a>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    if (recentDiv.querySelector('.text-muted')) {
        recentDiv.innerHTML = '';
    }
    
    recentDiv.insertBefore(importItem, recentDiv.firstChild);
}

function showSuccess(message) {
    Swal.fire({
        title: 'Succes!',
        text: message,
        icon: 'success',
        timer: 3000
    });
}

function showError(message) {
    Swal.fire({
        title: 'Fejl!',
        text: message,
        icon: 'error'
    });
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}
</script>

<style>
.import-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
}

.card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.btn {
    border-radius: 10px;
    font-weight: 600;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.progress {
    height: 20px;
    border-radius: 10px;
}

.alert {
    border-radius: 10px;
}
</style>
{% endblock %}
