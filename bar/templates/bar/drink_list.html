{% extends 'bar/base.html' %}

{% block title %}Mine Drinks{% endblock %}

{% block content %}
<style>

    /* Full Width Layout - No Wasted Space */
    .container, .container-fluid {
        max-width: none !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .row {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* Page content full width */
    body {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Modern Muuri Grid Layout for Drinks */
    .muuri-grid {
        position: relative;
        width: 100%;
        min-height: 200px;
    }

    /* Base drink card sizing - works with <PERSON><PERSON> */
    .drink-item {
        position: absolute;
        margin: 10px;
        box-sizing: border-box;
    }

    /* MOBILE: EXACT COPY from my_bar.html that WORKS */
    @media (max-width: 576px) {
        .muuri-grid, #drink-list, #substitute-drink-list, #almost-drink-list {
            display: flex !important;
            flex-wrap: wrap !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        /* Default: 4 columns */
        .drink-item {
            flex: 0 0 25% !important;
            width: 25% !important;
            height: 120px !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        /* 6 columns */
        .size-6 .drink-item {
            flex: 0 0 16.666% !important;
            width: 16.666% !important;
            height: 90px !important;
        }

        /* 8 columns */
        .size-8 .drink-item {
            flex: 0 0 12.5% !important;
            width: 12.5% !important;
            height: 70px !important;
        }

        /* 10 columns */
        .size-10 .drink-item {
            flex: 0 0 10% !important;
            width: 10% !important;
            height: 60px !important;
        }

        /* 12 columns */
        .size-12 .drink-item {
            flex: 0 0 8.333% !important;
            width: 8.333% !important;
            height: 50px !important;
        }
    }

    /* Tablet and up: Dynamic sizing based on dropdown selection */
    @media (min-width: 577px) {
        .drink-item.size-4 { width: calc(25% - 20px) !important; }      /* 4 per row */
        .drink-item.size-6 { width: calc(16.666% - 20px) !important; }  /* 6 per row */
        .drink-item.size-8 { width: calc(12.5% - 20px) !important; }    /* 8 per row */
        .drink-item.size-10 { width: calc(10% - 20px) !important; }     /* 10 per row */
        .drink-item.size-12 { width: calc(8.333% - 20px) !important; }  /* 12 per row */

        /* Default to 4 columns for drinks if no size class */
        .drink-item:not(.size-4):not(.size-6):not(.size-8):not(.size-10):not(.size-12) {
            width: calc(25% - 20px) !important;
        }
    }

    /* Large screens: Ensure minimum card size */
    @media (min-width: 1400px) {
        .drink-item.size-12 { width: calc(8.333% - 20px); min-width: 200px; }
    }

    /* Smooth transitions for grid size changes */
    .drink-item {
        transition: width 0.3s ease, transform 0.3s ease;
    }

    /* Tablet and up: Dynamic sizing based on dropdown selection */
    @media (min-width: 577px) {
        .drink-item.size-4 { width: calc(25% - 20px) !important; }      /* 4 per row */
        .drink-item.size-6 { width: calc(16.666% - 20px) !important; }  /* 6 per row */
        .drink-item.size-8 { width: calc(12.5% - 20px) !important; }    /* 8 per row */
        .drink-item.size-10 { width: calc(10% - 20px) !important; }     /* 10 per row */
        .drink-item.size-12 { width: calc(8.333% - 20px) !important; }  /* 12 per row */

        /* Default to 4 columns for drinks if no size class */
        .drink-item:not(.size-4):not(.size-6):not(.size-8):not(.size-10):not(.size-12) {
            width: calc(25% - 20px) !important;
        }
    }

    /* Large screens: Ensure minimum card size */
    @media (min-width: 1400px) {
        .drink-item.size-12 { width: calc(8.333% - 20px); min-width: 200px; }
    }

    /* Smooth transitions for grid size changes */
    .drink-item {
        transition: width 0.3s ease, transform 0.3s ease;
    }

    /* Loading state for Muuri */
    .muuri-grid.loading .drink-item {
        opacity: 0.7;
        transform: scale(0.95);
        animation: pulse 1.5s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 0.7; transform: scale(0.95); }
        50% { opacity: 0.9; transform: scale(1); }
    }

    /* Epic Hero Section for Mine Drinks */
    .drinks-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        color: white;
        padding: 80px 0 60px 0;
        margin: -20px -15px 40px -15px;
        position: relative;
        overflow: hidden;
        min-height: 50vh;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .floating-drinks {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .drink {
        position: absolute;
        font-size: 2.5rem;
        opacity: 0.2;
        animation: floatDrink 6s ease-in-out infinite;
    }

    .drink-1 { top: 15%; left: 10%; animation-delay: 0s; }
    .drink-2 { top: 25%; right: 15%; animation-delay: 1.2s; }
    .drink-3 { top: 65%; left: 20%; animation-delay: 2.4s; }
    .drink-4 { bottom: 25%; right: 25%; animation-delay: 3.6s; }
    .drink-5 { bottom: 15%; left: 30%; animation-delay: 4.8s; }

    @keyframes floatDrink {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-15px) rotate(3deg); }
        50% { transform: translateY(-30px) rotate(-3deg); }
        75% { transform: translateY(-15px) rotate(2deg); }
    }

    .hero-content {
        text-align: center;
        position: relative;
        z-index: 2;
        max-width: 95%;
        margin: 0 auto;
        padding: 0 20px;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 15px;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        animation: titleGlow 3s ease-in-out infinite alternate;
    }

    @keyframes titleGlow {
        from { text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); }
        to { text-shadow: 0 4px 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.2); }
    }

    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 30px;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .hero-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 15px;
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 20px 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.05);
        background: rgba(255, 255, 255, 0.25);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
        font-size: 2rem;
        margin-bottom: 8px;
        animation: bounce 2s infinite;
    }

    .stat-card:nth-child(1) .stat-icon { animation-delay: 0s; }
    .stat-card:nth-child(2) .stat-icon { animation-delay: 0.2s; }
    .stat-card:nth-child(3) .stat-icon { animation-delay: 0.4s; }
    .stat-card:nth-child(4) .stat-icon { animation-delay: 0.6s; }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-8px); }
        60% { transform: translateY(-4px); }
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 5px;
        display: block;
    }

    .stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Dark mode for drinks hero */
    body.dark-mode .drinks-hero {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #4a4a4a 100%);
    }

    /* Drink Discovery Section */
    .drink-discovery {
        margin: 1rem 0;
        padding: 1.5rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .drink-discovery.compact {
        margin: 0.5rem 0;
        padding: 1rem;
    }

    .drink-discovery.collapsed .discovery-cards {
        display: none;
    }

    .discovery-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .discovery-header .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        position: relative;
    }

    .collapse-btn {
        background: rgba(0, 0, 0, 0.1);
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        color: #333;
        cursor: pointer;
        margin-left: auto;
        transition: all 0.3s ease;
    }

    .collapse-btn:hover {
        background: rgba(0, 0, 0, 0.2);
        transform: scale(1.1);
    }

    .collapse-icon {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .discovery-header .title-icon {
        font-size: 2rem;
    }

    .discovery-header .section-subtitle {
        color: #666;
        font-size: 1rem;
    }

    .discovery-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    .discovery-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .discovery-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .discovery-card .card-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .discovery-card .card-icon {
        font-size: 1.5rem;
    }

    .discovery-card .card-header h4 {
        color: #333;
        font-weight: 600;
        margin: 0;
    }

    /* Mood-based discovery */
    .mood-options {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }

    .mood-btn {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .mood-btn:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .mood-btn.active {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-color: #667eea;
    }

    .mood-suggestions {
        space-y: 0.5rem;
    }

    .suggestion-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 10px;
        margin-bottom: 0.5rem;
    }

    .drink-emoji {
        font-size: 1.2rem;
    }

    .drink-name {
        font-weight: 500;
        color: #333;
    }

    .mood-match {
        background: linear-gradient(45deg, #4ecdc4, #44a08d);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Trending section */
    .trending-list {
        space-y: 0.75rem;
    }

    .trending-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 10px;
        margin-bottom: 0.75rem;
    }

    .trend-rank {
        background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 0.9rem;
    }

    .trend-info {
        flex: 1;
    }

    .trend-name {
        display: block;
        font-weight: 600;
        color: #333;
    }

    .trend-growth {
        font-size: 0.8rem;
        color: #28a745;
        font-weight: 500;
    }

    .trend-arrow {
        font-size: 1.2rem;
    }

    /* Taste profile */
    .taste-profile {
        margin-bottom: 1rem;
    }

    .taste-meter {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.75rem;
    }

    .taste-meter label {
        min-width: 60px;
        font-weight: 500;
        color: #333;
    }

    .meter-bar {
        flex: 1;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }

    .meter-fill {
        height: 100%;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .taste-recommendation {
        background: #e8f5e8;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        color: #333;
    }

    /* Mobile fixes for discovery section */
    @media (max-width: 768px) {
        .drink-discovery {
            margin: 0.5rem 0;
            padding: 1rem;
        }

        .discovery-cards {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .discovery-card {
            padding: 1rem !important;
        }

        .mood-options {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 0.5rem !important;
        }

        .mood-btn {
            padding: 0.75rem 1rem !important;
            font-size: 0.9rem !important;
        }

        .trending-item {
            padding: 0.5rem !important;
        }

        .taste-meter {
            margin-bottom: 0.5rem !important;
        }

        .taste-meter label {
            min-width: 50px !important;
            font-size: 0.9rem !important;
        }

        /* Grid size select mobile optimization */
        .modern-select {
            min-height: 44px !important;
            font-size: 16px !important;
            padding: 0.75rem 1rem !important;
        }

        .control-section {
            margin-bottom: 1rem !important;
        }

        .control-label {
            font-size: 0.9rem !important;
            margin-bottom: 0.5rem !important;
        }

        /* Fix text visibility in discovery cards */
        .discovery-card .card-header h4 {
            color: #333 !important;
        }

        .suggestion-item .drink-name {
            color: #333 !important;
        }

        .trend-name {
            color: #333 !important;
        }

        .discovery-value {
            color: #333 !important;
        }

        .taste-recommendation {
            color: #333 !important;
        }

        /* Mobile image fixes for drinks */
        .drink-image {
            border-radius: 12px !important;
        }

        .drink-name {
            font-size: 0.9rem !important;
            line-height: 1.2 !important;
        }

        .grid-item {
            padding: 8px !important;
        }

        .drink-card {
            border-radius: 12px !important;
        }
    }

    /* Modern Control Panel */
    .drinks-control-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        transition: all 0.3s ease;
    }

    .drinks-control-panel:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    .control-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .control-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 700;
        color: #2c3e50;
        font-size: 0.95rem;
    }

    .label-icon {
        font-size: 1.1rem;
    }

    .modern-select {
        padding: 12px 15px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        font-size: 0.95rem;
        font-weight: 600;
        color: #2c3e50;
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px;
    }

    .modern-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-search {
        padding: 12px 15px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        font-size: 0.95rem;
        color: #2c3e50;
    }

    .modern-search:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: white;
        transform: scale(1.02);
    }

    .category-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .category-chip {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 20px;
        overflow: hidden;
    }

    .category-chip input[type="checkbox"] {
        display: none;
    }

    .chip-content {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 15px;
        background: rgba(102, 126, 234, 0.1);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 20px;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.85rem;
        color: #667eea;
    }

    .category-chip:hover .chip-content {
        background: rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    .category-chip input[type="checkbox"]:checked + .chip-content {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: transparent;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transform: translateY(-2px) scale(1.05);
    }

    .chip-icon {
        font-size: 1rem;
    }

    /* Dark mode for control panel */
    body.dark-mode .drinks-control-panel {
        background: rgba(40, 40, 60, 0.95);
        color: #e0e0e0;
        border-color: rgba(255, 255, 255, 0.1);
    }

    body.dark-mode .control-label {
        color: #e0e0e0;
    }

    body.dark-mode .modern-select,
    body.dark-mode .modern-search {
        background: rgba(50, 50, 70, 0.9);
        color: #e0e0e0;
        border-color: rgba(255, 255, 255, 0.2);
    }

    body.dark-mode .modern-select:focus,
    body.dark-mode .modern-search:focus {
        background: rgba(60, 60, 80, 1);
        border-color: #90CAF9;
    }

    body.dark-mode .chip-content {
        background: rgba(144, 202, 249, 0.2);
        color: #90CAF9;
        border-color: rgba(255, 255, 255, 0.2);
    }

    body.dark-mode .category-chip input[type="checkbox"]:checked + .chip-content {
        background: linear-gradient(135deg, #90CAF9, #CE93D8);
        color: #1a1a1a;
    }

    /* Small devices (tablets, 576px and up) */
    @media (min-width: 576px) {
        .grid-item.size-4 { /* 4 per row */
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 { /* 6 per row */
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 { /* 8 per row */
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .grid-item.size-10 { /* 10 per row */
            flex: 0 0 10%;
            max-width: 10%;
        }
        .grid-item.size-12 { /* 12 per row */
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Medium devices (desktops, 768px and up) */
    @media (min-width: 768px) {
        .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .grid-item.size-10 {
            flex: 0 0 10%;
            max-width: 10%;
        }
        .grid-item.size-12 {
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Large devices (large desktops, 992px and up) */
    @media (min-width: 992px) {
        .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .grid-item.size-10 {
            flex: 0 0 10%;
            max-width: 10%;
        }
        .grid-item.size-12 {
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Extra large devices (large desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .grid-item.size-4 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .grid-item.size-6 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
        .grid-item.size-8 {
            flex: 0 0 12.5%;
            max-width: 12.5%;
        }
        .grid-item.size-10 {
            flex: 0 0 10%;
            max-width: 10%;
        }
        .grid-item.size-12 {
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }
    }

    /* Remove specific col-Xth as they are now handled by .grid-item.size-X */
    /* .col-6th { flex: 0 0 16.66%; max-width: 16.66%; } */
    /* .col-8th { flex: 0 0 12.5%; max-width: 12.5%; } */

    /* MOBILE: Simple card styling */
    @media (max-width: 576px) {
        .drink-card {
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            border: 1px solid #ddd !important;
            border-radius: 0 !important;
        }

        .drink-image-container {
            height: 70% !important;
        }

        .drink-name {
            font-size: 0.6rem !important;
            padding: 2px !important;
        }
    }

    .drink-card {
        position: relative;
        border: 2px solid rgba(255, 255, 255, 0.4);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        border-radius: 20px;
        overflow: hidden;
        cursor: pointer;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(20px) saturate(180%);
        -webkit-backdrop-filter: blur(20px) saturate(180%);
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        transform: translateY(0) scale(1);
        opacity: 0;
        animation: slideInUp 0.6s ease forwards;
    }

    .drink-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(25px) saturate(200%);
        -webkit-backdrop-filter: blur(25px) saturate(200%);
    }

    .drink-card:active {
        transform: translateY(-4px) scale(1.01);
        transition: all 0.1s ease;
    }

    /* Stagger animation for cards */
    .drink-item:nth-child(1) .drink-card { animation-delay: 0.1s; }
    .drink-item:nth-child(2) .drink-card { animation-delay: 0.2s; }
    .drink-item:nth-child(3) .drink-card { animation-delay: 0.3s; }
    .drink-item:nth-child(4) .drink-card { animation-delay: 0.4s; }
    .drink-item:nth-child(5) .drink-card { animation-delay: 0.5s; }
    .drink-item:nth-child(6) .drink-card { animation-delay: 0.6s; }
    .drink-item:nth-child(7) .drink-card { animation-delay: 0.7s; }
    .drink-item:nth-child(8) .drink-card { animation-delay: 0.8s; }
    .drink-item:nth-child(n+9) .drink-card { animation-delay: 0.9s; }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .drink-image-container {
        height: 250px;
        overflow: hidden;
    }

    .drink-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        filter: brightness(1) saturate(1);
        border-radius: 12px;
    }

    .drink-card:hover .drink-image {
        transform: scale(1.08);
        filter: brightness(1.1) saturate(1.2);
    }

    .drink-name {
        text-align: center;
        font-weight: bold;
        font-size: 1.2rem;
        margin-top: 10px;
        color: #0d6efd;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .drink-name::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: -100%;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, transparent, #0d6efd, transparent);
        transition: left 0.5s ease;
    }

    .drink-card:hover .drink-name {
        color: #0056b3;
        transform: translateY(-2px);
    }

    .drink-card:hover .drink-name::after {
        left: 100%;
    }

    .favorite-star {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 1.4rem;
        cursor: pointer;
        z-index: 10;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        transform-origin: center;
    }

    .favorite-star:hover {
        transform: scale(1.3) rotate(15deg);
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
    }

    .favorite-star.favorited {
        color: #ffd700;
        animation: starPulse 2s ease-in-out infinite;
    }

    .favorite-star.not-favorited {
        color: #ccc;
        opacity: 0.7;
    }

    .favorite-star:active {
        transform: scale(0.9);
        transition: all 0.1s ease;
    }

    @keyframes starPulse {
        0%, 100% {
            transform: scale(1);
            filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.4));
        }
        50% {
            transform: scale(1.1);
            filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
        }
    }

    .almost-drinks-section {
        margin-top: 50px;
        padding-top: 30px;
        border-top: 2px solid #dee2e6;
    }

    .almost-drink-card {
        position: relative;
        opacity: 0.9;
        border: 2px dashed rgba(220, 53, 69, 0.6);
        animation: almostPulse 3s ease-in-out infinite;
        background: rgba(220, 53, 69, 0.1);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }

    .almost-drink-card:hover {
        opacity: 1;
        border-color: #ff6b6b;
        animation: none;
        background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.15));
    }

    @keyframes almostPulse {
        0%, 100% {
            border-color: #dc3545;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        50% {
            border-color: #ff6b6b;
            box-shadow: 0 4px 20px rgba(220, 53, 69, 0.2);
        }
    }

    .missing-ingredient {
        position: absolute;
        bottom: 10px;
        left: 10px;
        right: 10px;
        background: rgba(220, 53, 69, 0.8);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        color: white;
        padding: 10px 15px;
        border-radius: 25px;
        font-size: 0.85rem;
        text-align: center;
        font-weight: bold;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        transform: translateY(0);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .almost-drink-card:hover .missing-ingredient {
        transform: translateY(-5px);
        background: linear-gradient(135deg, rgba(255, 107, 107, 0.95), rgba(255, 71, 87, 0.95));
        box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
    }

    /* Substitute drinks styling */
    .substitute-drink-card {
        position: relative;
        opacity: 0.95;
        border: 2px solid rgba(23, 162, 184, 0.6);
        animation: substitutePulse 4s ease-in-out infinite;
        background: rgba(23, 162, 184, 0.1);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }

    .substitute-drink-card:hover {
        opacity: 1;
        border-color: #17a2b8;
        animation: none;
        background: rgba(23, 162, 184, 0.15);
    }



    @keyframes substitutePulse {
        0%, 100% {
            border-color: rgba(23, 162, 184, 0.6);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        50% {
            border-color: #17a2b8;
            box-shadow: 0 4px 20px rgba(23, 162, 184, 0.3);
        }
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        min-height: 100vh;
        background-color: rgba(0, 0, 0, 0);
        display: none;
        z-index: 2000;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(0px);
        padding: 20px;
        box-sizing: border-box;
    }

    .modal-overlay.show {
        display: flex;
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(5px);
    }

    .modal-box {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(30px) saturate(180%);
        -webkit-backdrop-filter: blur(30px) saturate(180%);
        max-width: 600px;
        width: 90%;
        max-height: 85vh;
        overflow-y: auto;
        padding: 30px;
        border-radius: 28px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        border: 2px solid rgba(255, 255, 255, 0.5);
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0.8);
    }

    .modal-overlay.show .modal-box {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.8) translateY(30px) rotateX(10deg);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0) rotateX(0deg);
        }
    }

    .modal-box img {
        max-width: 100%;
        max-height: 300px;
        width: auto;
        height: auto;
        border-radius: 8px;
        margin-bottom: 15px;
        object-fit: contain;
        object-position: center;
    }

    .modal-box h4 {
        margin-bottom: 10px;
        font-size: 1.5rem;
        font-weight: bold;
        text-align: center;
    }

    .modal-box pre {
        background-color: #f8f9fa;
        padding: 12px;
        border-radius: 8px;
        white-space: pre-wrap;
        font-size: 1rem;
        line-height: 1.5;
        color: #212529;
        width: 100%;
    }

    /* Styling for recipe content in modal */
    #modalDrinkRecipe {
        background: rgba(248, 249, 250, 0.6);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 20px;
        border-radius: 16px;
        font-size: 1rem;
        line-height: 1.6;
        color: #212529;
        width: 100%;
    }

    #modalDrinkRecipe p {
        margin-bottom: 10px;
    }

    #modalDrinkRecipe ul, #modalDrinkRecipe ol {
        margin-bottom: 10px;
        padding-left: 20px;
    }

    #modalDrinkRecipe li {
        margin-bottom: 5px;
    }

    #modalDrinkRecipe h1, #modalDrinkRecipe h2, #modalDrinkRecipe h3,
    #modalDrinkRecipe h4, #modalDrinkRecipe h5, #modalDrinkRecipe h6 {
        margin-top: 15px;
        margin-bottom: 10px;
        font-weight: bold;
    }

    #modalDrinkRecipe strong {
        font-weight: bold;
    }

    body.dark-mode #modalDrinkRecipe {
        background: rgba(43, 43, 43, 0.6);
        border-color: rgba(255, 255, 255, 0.1);
        color: #eee;
    }

    /* Modal substitutions styling */
    #modalSubstitutions {
        background: rgba(23, 162, 184, 0.1);
        border-color: rgba(23, 162, 184, 0.3);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    body.dark-mode #modalSubstitutions {
        background: rgba(23, 162, 184, 0.2);
        border-color: rgba(23, 162, 184, 0.4);
        color: #eee;
    }

    .modal-close {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 1.8rem;
        font-weight: bold;
        color: #333;
        cursor: pointer;
    }

    .modal-favorite {
        position: absolute;
        top: 10px;
        left: 15px;
        font-size: 1.5rem;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .modal-favorite:hover {
        transform: scale(1.2);
    }

    body.dark-mode .drink-card {
        background: rgba(31, 31, 31, 0.7);
        border-color: rgba(255, 255, 255, 0.2);
        color: #eee;
    }

    body.dark-mode .drink-card:hover {
        background: rgba(31, 31, 31, 0.9);
        border-color: rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(25px) saturate(150%);
        -webkit-backdrop-filter: blur(25px) saturate(150%);
    }

    body.dark-mode .modal-box {
        background: rgba(31, 31, 31, 0.8);
        border-color: rgba(255, 255, 255, 0.2);
        color: #eee;
    }

    body.dark-mode .almost-drinks-section {
        border-top-color: #444;
    }

    body.dark-mode .substitute-drink-card {
        background: rgba(23, 162, 184, 0.2);
        border-color: rgba(23, 162, 184, 0.8);
    }

    body.dark-mode .substitute-drink-card:hover {
        background: rgba(23, 162, 184, 0.3);
    }



    /* Fix text colors in dark mode */
    body.dark-mode .text-muted,
    body.dark-mode small.text-muted {
        color: #bbb !important;
    }

    body.dark-mode .text-danger {
        color: #ff6b6b !important;
    }

    body.dark-mode .text-warning {
        color: #ffd93d !important;
    }

    body.dark-mode .drink-name {
        color: #90CAF9;
    }

    body.dark-mode .drink-card:hover .drink-name {
        color: #64B5F6;
    }

    /* Rating stars */
    .rating-stars {
        display: inline-block;
        font-size: 1.5rem;
        color: #ccc;
        cursor: pointer;
        margin-bottom: 10px;
    }
    .rating-stars span {
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        display: inline-block;
        transform: scale(1);
    }
    .rating-stars span:hover {
        transform: scale(1.3) rotate(10deg);
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
    }
    .rating-stars span.filled {
        color: #ffd700;
        animation: starShine 1.5s ease-in-out infinite alternate;
    }
    .rating-stars span:active {
        transform: scale(0.9);
        transition: all 0.1s ease;
    }

    @keyframes starShine {
        0% {
            filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.4));
        }
        100% {
            filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
        }
    }

    /* Modal Rating section */
    .modal-rating-section {
        text-align: center;
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;
        width: 100%;
    }
    body.dark-mode .modal-rating-section {
        border-top-color: #444;
    }
    .modal-rating-section h5 {
        margin-bottom: 10px;
    }

    /* Comments section */
    .comments-section {
        width: 100%;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    body.dark-mode .comments-section {
        border-top-color: #444;
    }
    .comments-section h5 {
        margin-bottom: 15px;
    }
    .comment-item {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #dee2e6;
    }
    body.dark-mode .comment-item {
        background-color: #2b2b2b;
        border-color: #444;
    }
    .comment-item strong {
        color: #0d6efd;
    }
    body.dark-mode .comment-item strong {
        color: #90CAF9;
    }
    .comment-item .comment-date {
        font-size: 0.8em;
        color: #6c757d;
        margin-left: 10px;
    }
    body.dark-mode .comment-item .comment-date {
        color: #bbb;
    }
    .comment-form textarea {
        resize: vertical;
    }

    /* Additional dark mode fixes */
    body.dark-mode .btn-outline-primary {
        color: #90CAF9;
        border-color: #90CAF9;
    }

    body.dark-mode .btn-outline-primary:hover {
        background-color: #90CAF9;
        color: #1f1f1f;
    }

    body.dark-mode .form-control {
        background-color: #2b2b2b;
        border-color: #444;
        color: #eee;
    }

    body.dark-mode .form-control:focus {
        background-color: #2b2b2b;
        border-color: #90CAF9;
        color: #eee;
        box-shadow: 0 0 0 0.2rem rgba(144, 202, 249, 0.25);
    }

    body.dark-mode .form-control::placeholder {
        color: #bbb;
    }

    /* Glasmorphism background */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-attachment: fixed;
        min-height: 100vh;
    }

    body.dark-mode {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        background-attachment: fixed;
    }

    /* Fallback for browsers without backdrop-filter support */
    @supports not (backdrop-filter: blur(10px)) {
        .drink-card {
            background: rgba(255, 255, 255, 0.95) !important;
        }

        .modal-box {
            background: rgba(255, 255, 255, 0.98) !important;
        }

        body.dark-mode .drink-card {
            background: rgba(31, 31, 31, 0.95) !important;
        }

        body.dark-mode .modal-box {
            background: rgba(31, 31, 31, 0.98) !important;
        }
    }

    /* Page load animations */
    .page-container {
        opacity: 0;
        animation: pageSlideIn 0.8s ease forwards;
    }

    @keyframes pageSlideIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Smooth search bar animation */
    #drinkSearch {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    #drinkSearch:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        transform: scale(1.02);
    }

    /* Category checkbox animations */
    .category-checkbox {
        transition: all 0.3s ease;
    }

    .btn:has(.category-checkbox:checked) {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
    }

    /* Mobile modal adjustments */
    @media (max-width: 768px) {
        .modal-overlay {
            padding: 10px;
        }

        .modal-box {
            width: 95%;
            max-height: 90vh;
        }
    }
</style>

<div class="page-container">
<h1 class="mb-4">Dine drinks</h1>

<!-- Epic Hero Section for Mine Drinks -->
<div class="drinks-hero" data-aos="fade-down" data-aos-duration="800">
    <div class="hero-background">
        <div class="floating-drinks">
            <div class="drink drink-1">🍹</div>
            <div class="drink drink-2">🍸</div>
            <div class="drink drink-3">🥃</div>
            <div class="drink drink-4">🍷</div>
            <div class="drink drink-5">🍺</div>
        </div>
    </div>
    <div class="hero-content">
        <h1 class="hero-title animate__animated animate__bounceInDown">🍹 Mine Drinks</h1>
        <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="300">Opdag fantastiske drinks du kan lave med din bar</p>
        <div class="hero-stats" data-aos="fade-up" data-aos-delay="600">
            <div class="stat-card" data-aos="zoom-in" data-aos-delay="700">
                <div class="stat-icon">🍹</div>
                <div class="stat-number" id="totalDrinks">{{ drinks|length }}</div>
                <div class="stat-label">Tilgængelige</div>
            </div>
            <div class="stat-card" data-aos="zoom-in" data-aos-delay="800">
                <div class="stat-icon">⭐</div>
                <div class="stat-number" id="favoriteDrinks">0</div>
                <div class="stat-label">Favoritter</div>
            </div>
            <div class="stat-card" data-aos="zoom-in" data-aos-delay="900">
                <div class="stat-icon">🔍</div>
                <div class="stat-number" id="visibleDrinks">{{ drinks|length }}</div>
                <div class="stat-label">Vises</div>
            </div>
            <div class="stat-card" data-aos="zoom-in" data-aos-delay="1000">
                <div class="stat-icon">🎯</div>
                <div class="stat-number" id="selectedCategories">0</div>
                <div class="stat-label">Kategorier</div>
            </div>
        </div>
    </div>
</div>

<!-- Drink Discovery Section - Compact -->
<div class="drink-discovery compact" data-aos="fade-up">
    <div class="discovery-header">
        <h3 class="section-title">
            <span class="title-icon">🎯</span>
            Drink Discovery
            <button class="collapse-btn" onclick="toggleSection('discovery')">
                <span class="collapse-icon">−</span>
            </button>
        </h3>
    </div>

    <div class="discovery-cards">
        <div class="discovery-card mood-based" data-aos="slide-up" data-aos-delay="100">
            <div class="card-header">
                <div class="card-icon">🌟</div>
                <h4>Baseret på Stemning</h4>
            </div>
            <div class="mood-options">
                <button class="mood-btn active" data-mood="chill">😌 Chill</button>
                <button class="mood-btn" data-mood="party">🎉 Fest</button>
                <button class="mood-btn" data-mood="romantic">💕 Romantisk</button>
                <button class="mood-btn" data-mood="summer">☀️ Sommer</button>
            </div>
            <div class="mood-suggestions">
                <div class="suggestion-item">
                    <span class="drink-emoji">🍸</span>
                    <span class="drink-name">Gin & Tonic</span>
                    <span class="mood-match">95% match</span>
                </div>
                <div class="suggestion-item">
                    <span class="drink-emoji">🥃</span>
                    <span class="drink-name">Old Fashioned</span>
                    <span class="mood-match">87% match</span>
                </div>
            </div>
        </div>

        <div class="discovery-card trending-now" data-aos="slide-up" data-aos-delay="200">
            <div class="card-header">
                <div class="card-icon">🔥</div>
                <h4>Trending Lige Nu</h4>
            </div>
            <div class="trending-list">
                <div class="trending-item">
                    <div class="trend-rank">#1</div>
                    <div class="trend-info">
                        <span class="trend-name">Aperol Spritz</span>
                        <span class="trend-growth">+156% denne uge</span>
                    </div>
                    <div class="trend-arrow">📈</div>
                </div>
                <div class="trending-item">
                    <div class="trend-rank">#2</div>
                    <div class="trend-info">
                        <span class="trend-name">Espresso Martini</span>
                        <span class="trend-growth">+89% denne uge</span>
                    </div>
                    <div class="trend-arrow">📈</div>
                </div>
                <div class="trending-item">
                    <div class="trend-rank">#3</div>
                    <div class="trend-info">
                        <span class="trend-name">Negroni</span>
                        <span class="trend-growth">+67% denne uge</span>
                    </div>
                    <div class="trend-arrow">📈</div>
                </div>
            </div>
        </div>

        <div class="discovery-card personal-taste" data-aos="slide-up" data-aos-delay="300">
            <div class="card-header">
                <div class="card-icon">🎨</div>
                <h4>Din Smagsprofile</h4>
            </div>
            <div class="taste-profile">
                <div class="taste-meter">
                    <label>Sød</label>
                    <div class="meter-bar">
                        <div class="meter-fill" style="width: 75%"></div>
                    </div>
                </div>
                <div class="taste-meter">
                    <label>Sur</label>
                    <div class="meter-bar">
                        <div class="meter-fill" style="width: 60%"></div>
                    </div>
                </div>
                <div class="taste-meter">
                    <label>Bitter</label>
                    <div class="meter-bar">
                        <div class="meter-fill" style="width: 40%"></div>
                    </div>
                </div>
                <div class="taste-meter">
                    <label>Stærk</label>
                    <div class="meter-bar">
                        <div class="meter-fill" style="width: 85%"></div>
                    </div>
                </div>
            </div>
            <div class="taste-recommendation">
                <strong>Anbefaling:</strong> Du elsker stærke, søde drinks. Prøv en Whiskey Sour!
            </div>
        </div>
    </div>
</div>

<!-- Modern Control Panel -->
<div class="drinks-control-panel" data-aos="fade-up" data-aos-delay="200">
    <div class="control-section">
        <label class="control-label">
            <span class="label-icon">📐</span>
            Layout
        </label>
        <select id="gridSizeSelect" class="modern-select">
            <option value="4" selected>4 per række</option>
            <option value="6">6 per række</option>
            <option value="8">8 per række</option>
            <option value="10">10 per række</option>
            <option value="12">12 per række</option>
        </select>
    </div>

    <div class="control-section">
        <label class="control-label">
            <span class="label-icon">🔍</span>
            Søg Drinks
        </label>
        <input type="text" class="modern-search" placeholder="Søg efter drinks..." id="drinkSearch">
    </div>

    <div class="control-section">
        <label class="control-label">
            <span class="label-icon">🏷️</span>
            Kategorier
        </label>
        <div class="category-chips">
            <label class="category-chip">
                <input type="checkbox" class="category-checkbox" value="drink">
                <span class="chip-content">
                    <span class="chip-icon">🍹</span>
                    <span class="chip-text">Drinks</span>
                </span>
            </label>
            <label class="category-chip">
                <input type="checkbox" class="category-checkbox" value="cocktail">
                <span class="chip-content">
                    <span class="chip-icon">🍸</span>
                    <span class="chip-text">Cocktails</span>
                </span>
            </label>
            <label class="category-chip">
                <input type="checkbox" class="category-checkbox" value="shot">
                <span class="chip-content">
                    <span class="chip-icon">🔫</span>
                    <span class="chip-text">Shots</span>
                </span>
            </label>
            <label class="category-chip">
                <input type="checkbox" class="category-checkbox" value="nonalcoholic">
                <span class="chip-content">
                    <span class="chip-icon">🚫</span>
                    <span class="chip-text">Alkoholfri</span>
                </span>
            </label>
        </div>
    </div>
</div>

<!-- Old controls removed - now in modern control panel above -->

<div class="muuri-grid" id="drink-list">
    {% for drink in drinks %}
        <div class="drink-item size-4" data-aos="fade-up" data-aos-delay="{{ forloop.counter0|add:100 }}" data-type="{{ drink.drink_type }}" data-drink-id="{{ drink.id }}"
             data-name="{{ drink.name }}" data-image-url="{% if drink.image %}{{ drink.image.url }}{% endif %}"
             data-average-rating="{{ drink.average_rating|floatformat:1 }}"
             data-user-rating="{{ drink.user_rating|default:'0' }}">
            <div class="card drink-card" onclick="openModal('{{ drink.id }}')">
                {% if drink.image %}
                    <div class="drink-image-container">
                        <img src="{{ drink.image.url }}" alt="{{ drink.name }}" class="drink-image">
                    </div>
                {% endif %}
                <div class="card-body">
                    <div class="drink-name">{{ drink.name }}</div>
                    <div class="text-center mt-2">
                        {% if drink.average_rating > 0 %}
                            <span class="text-warning">
                                {% for _ in drink.filled_stars %}★{% endfor %}{% for _ in drink.empty_stars %}☆{% endfor %}
                            </span>
                            <small class="text-muted">({{ drink.average_rating|floatformat:1 }})</small>
                        {% else %}
                            <small class="text-muted">Ingen bedømmelser</small>
                        {% endif %}
                    </div>
                </div>
                <div class="favorite-star {% if drink.favorite %}favorited{% else %}not-favorited{% endif %}"
                     onclick="event.stopPropagation(); toggleFavorite({{ drink.id }}, this)">★</div>
            </div>
        </div>
    {% endfor %}
</div>

{% if substitute_drinks %}
<div class="almost-drinks-section">
    <h2 class="mb-4">🔄 Kan laves med erstatninger</h2>
    <div class="muuri-grid" id="substitute-drink-list">
        {% for drink in substitute_drinks %}
            <div class="drink-item substitute-drink-item size-4" data-type="{{ drink.drink_type }}" data-drink-id="{{ drink.id }}"
                 data-name="{{ drink.name }}" data-image-url="{% if drink.image %}{{ drink.image.url }}{% endif %}"
                 data-recipe="{{ drink.recipe }}" data-average-rating="{{ drink.average_rating|floatformat:1 }}"
                 data-category="{{ drink.drink_type }}"
                 data-substitutions="{% for sub in drink.substitutions %}{{ sub.original }}→{{ sub.substitutes|join:',' }}{% if not forloop.last %}|{% endif %}{% endfor %}">
                    <div class="card drink-card substitute-drink-card" onclick="openModal({{ drink.id }})">
                        {% if drink.image %}
                            <div class="drink-image-container">
                                <img src="{{ drink.image.url }}" alt="{{ drink.name }}" class="drink-image">
                            </div>
                        {% endif %}
                        <div class="card-body">
                            <div class="drink-name">{{ drink.name }}</div>
                            <div class="text-center mt-2">
                                {% if drink.average_rating > 0 %}
                                    <span class="text-warning">
                                        {% for _ in drink.filled_stars %}★{% endfor %}{% for _ in drink.empty_stars %}☆{% endfor %}
                                    </span>
                                    <small class="text-muted">({{ drink.average_rating|floatformat:1 }})</small>
                                {% else %}
                                    <small class="text-muted">Ingen bedømmelser</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                <div class="favorite-star {% if drink.favorite %}favorited{% else %}not-favorited{% endif %}"
                     onclick="event.stopPropagation(); toggleFavorite({{ drink.id }}, this)">★</div>
            </div>
        {% endfor %}
    </div>
</div>
{% endif %}

{% if almost_drinks %}
<div class="almost-drinks-section">
    <h2 class="mb-4">🤏 Næsten der - mangler kun 1 ingrediens</h2>
    <div class="muuri-grid" id="almost-drink-list">
        {% for drink in almost_drinks %}
            <div class="drink-item almost-drink-item size-4" data-type="{{ drink.drink_type }}" data-drink-id="{{ drink.id }}"
                 data-name="{{ drink.name }}" data-image-url="{% if drink.image %}{{ drink.image.url }}{% endif %}"
                 data-average-rating="{{ drink.average_rating|floatformat:1 }}"
                 data-user-rating="{{ drink.user_rating|default:'0' }}" data-missing-ingredient="{{ drink.missing_ingredient }}">
                <div class="card drink-card almost-drink-card" onclick="openModal('{{ drink.id }}')">
                    {% if drink.image %}
                        <div class="drink-image-container">
                            <img src="{{ drink.image.url }}" alt="{{ drink.name }}" class="drink-image">
                        </div>
                    {% endif %}
                    <div class="card-body">
                        <div class="drink-name">{{ drink.name }}</div>
                        <div class="text-center mt-2">
                            {% if drink.average_rating > 0 %}
                                <span class="text-warning">
                                    {% for _ in drink.filled_stars %}★{% endfor %}{% for _ in drink.empty_stars %}☆{% endfor %}
                                </span>
                                <small class="text-muted">({{ drink.average_rating|floatformat:1 }})</small>
                            {% else %}
                                <small class="text-muted">Ingen bedømmelser</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="missing-ingredient">
                        Mangler: {{ drink.missing_ingredient }}
                    </div>
                    <div class="favorite-star {% if drink.favorite %}favorited{% else %}not-favorited{% endif %}"
                         onclick="event.stopPropagation(); toggleFavorite({{ drink.id }}, this)">★</div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<div id="drinkModal" class="modal-overlay">
    <div class="modal-box">
        <span class="modal-close" onclick="closeModal()">&times;</span>
        <div class="modal-favorite" onclick="toggleFavoriteFromModal(this)">★</div>
        <h4 id="modalDrinkName"></h4>
        <img id="modalDrinkImage" src="" alt="" style="display: none;">
        <div class="alert alert-warning mb-3" id="modalMissingIngredient" style="display: none;">
            <strong>Mangler:</strong> <span id="modalMissingIngredientText"></span>
        </div>
        <div class="alert alert-info mb-3" id="modalSubstitutions" style="display: none;">
            <strong>🔄 Kan laves med erstatninger:</strong>
            <div id="modalSubstitutionsContent"></div>
        </div>
        <div style="text-align: left;" id="modalDrinkRecipe"></div>

        <div class="modal-rating-section">
            <h5 class="mb-2">Bedøm denne drink:</h5>
            <div class="rating-stars" id="modalRatingStars" data-drink-id="">
                <span data-stars="1">★</span>
                <span data-stars="2">★</span>
                <span data-stars="3">★</span>
                <span data-stars="4">★</span>
                <span data-stars="5">★</span>
            </div>
            <div class="text-center">
                Gennemsnitlig bedømmelse: <span id="modalAverageRating"></span> (<span id="modalNumRatings"></span> bedømmelser)
            </div>
        </div>

        <div class="comments-section">
            <h5>Kommentarer:</h5>
            <div id="commentsList">
            </div>
            <div class="comment-form mt-3">
                <textarea class="form-control" id="commentText" rows="3" placeholder="Skriv din kommentar her..."></textarea>
                <button class="btn btn-primary btn-sm mt-2" id="submitComment">Send kommentar</button>
            </div>
        </div>
    </div>
</div>

<script>
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');

    function toggleFavorite(drinkId, element) {
        fetch('{% url "toggle_favorite" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrftoken,
            },
            body: 'drink_id=' + drinkId
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'added') {
                document.querySelectorAll(`[data-drink-id="${drinkId}"] .favorite-star`).forEach(star => {
                    star.classList.remove('not-favorited');
                    star.classList.add('favorited');
                });
            } else if (data.status === 'removed') {
                document.querySelectorAll(`[data-drink-id="${drinkId}"] .favorite-star`).forEach(star => {
                    star.classList.remove('favorited');
                    star.classList.add('not-favorited');
                });
            }

            // Update hero stats after favorite change
            if (typeof updateHeroStats === 'function') {
                updateHeroStats();
            }

            // Move favorited items to top with smooth animation
            if (data.status === 'added') {
                const drinkItem = document.querySelector(`.drink-item[data-drink-id="${drinkId}"]`);
                if (drinkItem) {
                    moveCardToTop(drinkItem);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    function toggleFavoriteFromModal(element) {
        const drinkId = document.getElementById('modalRatingStars').dataset.drinkId;
        // Find the corresponding favorite star on the main page to update its state
        const mainCardStar = document.querySelector(`.drink-item[data-drink-id="${drinkId}"] .favorite-star`);
        toggleFavorite(drinkId, mainCardStar);
    }

    function moveCardToTop(drinkItem) {
        const container = drinkItem.parentElement;
        const firstChild = container.firstElementChild;

        if (firstChild !== drinkItem) {
            // Add slide-out animation
            drinkItem.style.transition = 'all 0.4s ease';
            drinkItem.style.transform = 'translateX(-100%)';
            drinkItem.style.opacity = '0.5';

            setTimeout(() => {
                container.prepend(drinkItem);
                // Reset and add slide-in animation
                drinkItem.style.transform = 'translateX(100%)';
                requestAnimationFrame(() => {
                    drinkItem.style.transform = 'translateX(0)';
                    drinkItem.style.opacity = '1';
                });

                // Reset transition after animation
                setTimeout(() => {
                    drinkItem.style.transition = '';
                }, 400);
            }, 400);
        }
    }

    function openModal(id) {
        const drinkItem = document.querySelector(`.drink-item[data-drink-id="${id}"]`);
        if (!drinkItem) return;

        const modal = document.getElementById('drinkModal');
        const modalDrinkName = document.getElementById('modalDrinkName');
        const modalDrinkImage = document.getElementById('modalDrinkImage');
        const modalDrinkRecipe = document.getElementById('modalDrinkRecipe');
        const modalMissingIngredient = document.getElementById('modalMissingIngredient');
        const modalMissingIngredientText = document.getElementById('modalMissingIngredientText');
        const modalFavorite = modal.querySelector('.modal-favorite');
        const modalRatingStars = document.getElementById('modalRatingStars');
        const modalAverageRating = document.getElementById('modalAverageRating');
        const modalNumRatings = document.getElementById('modalNumRatings');
        const commentsList = document.getElementById('commentsList');
        const submitCommentBtn = document.getElementById('submitComment');
        const commentTextarea = document.getElementById('commentText');


        // Set data for modal
        modalDrinkName.textContent = drinkItem.dataset.name;
        if (drinkItem.dataset.imageUrl && drinkItem.dataset.imageUrl !== 'None') {
            modalDrinkImage.src = drinkItem.dataset.imageUrl;
            modalDrinkImage.style.display = 'block';
        } else {
            modalDrinkImage.style.display = 'none';
        }
        // Fetch recipe via AJAX
        modalDrinkRecipe.innerHTML = '<p class="text-muted">Indlæser opskrift...</p>';
        fetch(`{% url "get_drink_recipe" 0 %}`.replace('0', id), {
            method: 'GET',
            headers: {
                'X-CSRFToken': csrftoken,
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                modalDrinkRecipe.innerHTML = data.recipe;
            } else {
                modalDrinkRecipe.innerHTML = '<p class="text-danger">Kunne ikke indlæse opskrift.</p>';
            }
        })
        .catch(error => {
            console.error('Error fetching recipe:', error);
            modalDrinkRecipe.innerHTML = '<p class="text-danger">Fejl ved indlæsning af opskrift.</p>';
        });

        // Handle missing ingredient
        if (drinkItem.dataset.missingIngredient) {
            modalMissingIngredient.style.display = 'block';
            modalMissingIngredientText.textContent = drinkItem.dataset.missingIngredient;
        } else {
            modalMissingIngredient.style.display = 'none';
        }

        // Handle substitutions
        const modalSubstitutions = document.getElementById('modalSubstitutions');
        const modalSubstitutionsContent = document.getElementById('modalSubstitutionsContent');

        if (drinkItem.dataset.substitutions) {
            modalSubstitutions.style.display = 'block';
            const substitutions = drinkItem.dataset.substitutions.split('|');
            let substitutionsHtml = '<ul class="mb-0">';
            substitutions.forEach(sub => {
                const [original, replacements] = sub.split('→');
                substitutionsHtml += `<li><strong>${original}</strong> → ${replacements.split(',').join(', ')}</li>`;
            });
            substitutionsHtml += '</ul>';
            modalSubstitutionsContent.innerHTML = substitutionsHtml;
        } else {
            modalSubstitutions.style.display = 'none';
        }

        // Set favorite status in modal
        if (drinkItem.querySelector('.favorite-star').classList.contains('favorited')) {
            modalFavorite.classList.remove('not-favorited');
            modalFavorite.classList.add('favorited');
        } else {
            modalFavorite.classList.remove('favorited');
            modalFavorite.classList.add('not-favorited');
        }

        // Set up rating
        modalRatingStars.dataset.drinkId = id;
        const userRating = parseInt(drinkItem.dataset.userRating);
        updateStarsDisplay(userRating);

        const avgRating = parseFloat(drinkItem.dataset.averageRating);
        modalAverageRating.textContent = isNaN(avgRating) ? 'Ingen' : avgRating.toFixed(1) + ' ★';
        // (You'd need to fetch actual number of ratings from backend if you want this to be accurate)
        modalNumRatings.textContent = ' '; // Placeholder for now - actual number of ratings not easily available without another AJAX call or passing more data

        // Clear existing comments
        commentsList.innerHTML = '';
        // Fetch and display comments
        fetchCommentsForDrink(id, commentsList);


        // Event listeners for rating stars
        modalRatingStars.querySelectorAll('span').forEach(star => {
            star.onclick = function() {
                rateDrink(id, parseInt(this.dataset.stars));
            };
        });

        // Event listener for adding comment
        submitCommentBtn.onclick = function() {
            addComment(id, commentTextarea.value, commentsList, commentTextarea);
        };


        // Position modal based on current scroll position
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const viewportHeight = window.innerHeight;

        // Position the modal overlay to cover the entire document
        modal.style.height = Math.max(document.documentElement.scrollHeight, viewportHeight) + 'px';

        // Position the modal box in the center of the current viewport
        const modalBox = modal.querySelector('.modal-box');
        modalBox.style.top = (scrollTop + viewportHeight / 2) + 'px';

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Add show class with slight delay for smooth animation
        requestAnimationFrame(() => {
            modal.classList.add('show');
        });
    }

    function closeModal() {
        document.body.style.overflow = '';
        document.getElementById('drinkModal').classList.remove('show');
    }

    document.addEventListener('click', function(event) {
        const openModal = document.querySelector('.modal-overlay.show');
        if (openModal && event.target === openModal) {
            closeModal();
        }
    });

    // Combined search and filter functionality
    function applyFilters() {
        const searchQuery = document.getElementById('drinkSearch').value.toLowerCase();
        const selectedCategories = Array.from(document.querySelectorAll('.category-checkbox:checked'))
            .map(cb => cb.value);

        console.log('Applying filters:', { searchQuery, selectedCategories });

        document.querySelectorAll('.drink-item').forEach(item => {
            const name = item.querySelector('.drink-name').textContent.toLowerCase();
            const category = item.dataset.type; // Use data-type instead of data-category

            // Check search query
            const matchesSearch = !searchQuery || name.includes(searchQuery);

            // Check category filter
            const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(category);

            // Show item only if it matches both search and category
            const shouldShow = matchesSearch && matchesCategory;

            console.log('Item:', name, 'Category:', category, 'Should show:', shouldShow);

            if (shouldShow) {
                item.style.display = '';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            } else {
                item.style.opacity = '0';
                item.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    if (item.style.opacity === '0') {
                        item.style.display = 'none';
                    }
                }, 200);
            }
        });

        // Update results count
        updateResultsCount();

        // Update hero stats
        if (typeof updateHeroStats === 'function') {
            updateHeroStats();
        }
    }

    function updateResultsCount() {
        const hiddenItems = document.querySelectorAll('.drink-item[style*="display: none"]').length;
        const totalItems = document.querySelectorAll('.drink-item').length;
        const showing = totalItems - hiddenItems;

        let countElement = document.getElementById('resultsCount');
        if (!countElement) {
            countElement = document.createElement('div');
            countElement.id = 'resultsCount';
            countElement.className = 'text-muted mb-3';
            document.querySelector('h1').after(countElement);
        }

        if (showing === totalItems) {
            countElement.textContent = `Viser alle ${totalItems} drinks`;
        } else {
            countElement.textContent = `Viser ${showing} af ${totalItems} drinks`;
        }
    }

    // Event listeners
    document.getElementById('drinkSearch').addEventListener('input', applyFilters);

    document.querySelectorAll('.category-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', applyFilters);
    });

    // Initial count
    updateResultsCount();

    // --- GRID SIZE SELECT LOGIC FOR DRINK_LIST ---
    const gridSizeSelect = document.getElementById('gridSizeSelect');
    const drinkList = document.getElementById('drink-list');
    const almostDrinkList = document.getElementById('almost-drink-list');

    function updateDrinkGridSize(size) {
        // Apply to main drink list, substitute drinks list, and almost drinks list
        const items = document.querySelectorAll('#drink-list .drink-item, #substitute-drink-list .drink-item, #almost-drink-list .drink-item');

        items.forEach((item) => {
            item.classList.remove('size-4', 'size-6', 'size-8', 'size-10', 'size-12');
            item.classList.add('size-' + size);
        });

        // Refresh all Muuri grids after size change
        setTimeout(() => {
            Object.values(drinkGrids).forEach(grid => {
                if (grid) {
                    grid.refreshItems();
                    grid.layout(true);
                }
            });
        }, 300);


    }

    // Apply saved size on load
    const savedDrinkGridSize = localStorage.getItem('drinkGridSize') || '4'; // Default to 4 columns for drinks
    gridSizeSelect.value = savedDrinkGridSize;
    updateDrinkGridSize(savedDrinkGridSize);

    gridSizeSelect.addEventListener('change', function () {
        const selectedSize = this.value;
        localStorage.setItem('drinkGridSize', selectedSize); // Save with a different key
        updateDrinkGridSize(selectedSize);
    });

    // Duplicate filtering system removed - using the combined applyFilters function above

    // Rating functions
    function updateStarsDisplay(userRating) {
        const modalRatingStars = document.getElementById('modalRatingStars');
        modalRatingStars.querySelectorAll('span').forEach((star, index) => {
            if (index < userRating) {
                star.classList.add('filled');
            } else {
                star.classList.remove('filled');
            }
        });
    }

    function rateDrink(drinkId, stars) {
        fetch('{% url "rate_drink" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrftoken,
            },
            body: `drink_id=${drinkId}&stars=${stars}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updateStarsDisplay(data.user_rating);
                document.getElementById('modalAverageRating').textContent = data.average_rating + ' ★';
                // Update average rating on the card as well
                document.querySelector(`.drink-item[data-drink-id="${drinkId}"]`).dataset.averageRating = data.average_rating;
                document.querySelector(`.drink-item[data-drink-id="${drinkId}"]`).dataset.userRating = data.user_rating;
            } else {
                Swal.fire('Fejl!', 'Fejl ved bedømmelse: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire('Fejl!', 'Der opstod en fejl ved bedømmelse.', 'error');
        });
    }

    // Comment functions
    function fetchCommentsForDrink(drinkId, commentsListElement) {
        commentsListElement.innerHTML = '<p class="text-muted">Indlæser kommentarer...</p>'; // Show loading

        fetch(`{% url "get_comments" 0 %}`.replace('0', drinkId), { // Use a dummy ID in url template and replace
            method: 'GET',
            headers: {
                'X-CSRFToken': csrftoken,
            },
        })
        .then(response => response.json())
        .then(data => {
            commentsListElement.innerHTML = ''; // Clear loading message
            if (data.status === 'success' && data.comments && data.comments.length > 0) {
                data.comments.forEach(comment => {
                    const commentDiv = document.createElement('div');
                    commentDiv.classList.add('comment-item');
                    commentDiv.innerHTML = `<strong>${comment.username}</strong> <span class="comment-date">${comment.created_at}</span><p>${comment.text}</p>`;
                    commentsListElement.appendChild(commentDiv);
                });
            } else if (data.status === 'success') {
                commentsListElement.innerHTML = '<p class="text-muted">Ingen kommentarer endnu.</p>';
            } else {
                commentsListElement.innerHTML = `<p class="text-danger">Fejl ved indlæsning af kommentarer: ${data.message}</p>`;
            }
        })
        .catch(error => {
            console.error('Error fetching comments:', error);
            commentsListElement.innerHTML = '<p class="text-danger">Kunne ikke indlæse kommentarer på grund af en netværksfejl.</p>';
        });
    }


    function addComment(drinkId, commentText, commentsListElement, commentTextareaElement) {
        if (!commentText.trim()) {
            Swal.fire('Advarsel!', 'Kommentaren kan ikke være tom.', 'warning');
            return;
        }

        fetch('{% url "add_comment" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrftoken,
            },
            body: `drink_id=${drinkId}&comment_text=${encodeURIComponent(commentText)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Clear the "no comments yet" message if it exists
                const noCommentsMessage = commentsListElement.querySelector('.text-muted');
                if (noCommentsMessage && noCommentsMessage.textContent === 'Ingen kommentarer endnu.') {
                    commentsListElement.innerHTML = '';
                }

                const commentDiv = document.createElement('div');
                commentDiv.classList.add('comment-item');
                commentDiv.innerHTML = `<strong>${data.comment.username}</strong> <span class="comment-date">${data.comment.created_at}</span><p>${data.comment.text}</p>`;
                commentsListElement.prepend(commentDiv); // Add to top of comments
                commentTextareaElement.value = ''; // Clear textarea
            } else {
                Swal.fire('Fejl!', 'Fejl ved tilføjelse af kommentar: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire('Fejl!', 'Der opstod en fejl ved tilføjelse af kommentar.', 'error');
        });
    }

    // Update hero statistics
    function updateHeroStats() {
        const totalDrinks = document.querySelectorAll('.drink-item').length;
        const visibleDrinks = document.querySelectorAll('.drink-item:not([style*="display: none"])').length;
        const favoriteDrinks = document.querySelectorAll('.favorite-star.favorited').length;
        const selectedCategories = document.querySelectorAll('.category-checkbox:checked').length;

        document.getElementById('totalDrinks').textContent = totalDrinks;
        document.getElementById('visibleDrinks').textContent = visibleDrinks;
        document.getElementById('favoriteDrinks').textContent = favoriteDrinks;
        document.getElementById('selectedCategories').textContent = selectedCategories;
    }

    // Stats update is now integrated into applyFilters function above

    // Modern Muuri grid layout for drinks
    let drinkGrids = {};

    function initDrinkGrids() {
        // Check if Muuri is loaded
        if (typeof Muuri === 'undefined') {
            console.log('Muuri not loaded, skipping drink grids');
            return;
        }

        // Initialize main drink grid
        const mainGrid = document.getElementById('drink-list');
        if (mainGrid) {
            drinkGrids.main = new Muuri(mainGrid, {
                items: '.drink-item',
                layoutDuration: 400,
                layoutEasing: 'ease',
                dragEnabled: false,
                layout: {
                    fillGaps: true,
                    horizontal: false,
                    alignRight: false,
                    alignBottom: false,
                    rounding: true
                }
            });
        }

        // Initialize substitute drinks grid
        const substituteGrid = document.getElementById('substitute-drink-list');
        if (substituteGrid) {
            drinkGrids.substitute = new Muuri(substituteGrid, {
                items: '.drink-item',
                layoutDuration: 400,
                layoutEasing: 'ease',
                dragEnabled: false,
                layout: { fillGaps: true, horizontal: false, rounding: true }
            });
        }

        // Initialize almost drinks grid
        const almostGrid = document.getElementById('almost-drink-list');
        if (almostGrid) {
            drinkGrids.almost = new Muuri(almostGrid, {
                items: '.drink-item',
                layoutDuration: 400,
                layoutEasing: 'ease',
                dragEnabled: false,
                layout: { fillGaps: true, horizontal: false, rounding: true }
            });
        }

        console.log('Drink grids initialized successfully');
    }

    // Initialize modern libraries and stats
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 600,
            easing: 'ease-in-out',
            once: true,
            offset: 50
        });

        // Initialize Muuri grids for all drink sections
        initDrinkGrids();

        // Small delay to ensure all elements are rendered
        setTimeout(() => {
            updateHeroStats();
        }, 100);
    });

    // Favorite stats update is now integrated into the main toggleFavorite function above

    // Mood-based discovery functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Mood button functionality
        const moodButtons = document.querySelectorAll('.mood-btn');
        const moodSuggestions = document.querySelector('.mood-suggestions');

        const moodData = {
            chill: [
                { emoji: '🍸', name: 'Gin & Tonic', match: '95%' },
                { emoji: '🥃', name: 'Old Fashioned', match: '87%' },
                { emoji: '🍷', name: 'Wine Spritzer', match: '82%' }
            ],
            party: [
                { emoji: '🍾', name: 'Champagne Cocktail', match: '98%' },
                { emoji: '🍹', name: 'Piña Colada', match: '92%' },
                { emoji: '🥂', name: 'Aperol Spritz', match: '89%' }
            ],
            romantic: [
                { emoji: '🍷', name: 'French 75', match: '96%' },
                { emoji: '🌹', name: 'Rose Martini', match: '91%' },
                { emoji: '💕', name: 'Love Potion', match: '88%' }
            ],
            summer: [
                { emoji: '🍹', name: 'Mojito', match: '97%' },
                { emoji: '🍊', name: 'Aperol Spritz', match: '94%' },
                { emoji: '🥥', name: 'Piña Colada', match: '90%' }
            ]
        };

        moodButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                moodButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Get mood and update suggestions
                const mood = this.dataset.mood;
                const suggestions = moodData[mood] || moodData.chill;

                // Update suggestions with animation
                moodSuggestions.style.opacity = '0';

                setTimeout(() => {
                    moodSuggestions.innerHTML = suggestions.map(drink => `
                        <div class="suggestion-item">
                            <span class="drink-emoji">${drink.emoji}</span>
                            <span class="drink-name">${drink.name}</span>
                            <span class="mood-match">${drink.match} match</span>
                        </div>
                    `).join('');

                    moodSuggestions.style.opacity = '1';
                }, 150);
            });
        });

        // Animate taste meters on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const meters = entry.target.querySelectorAll('.meter-fill');
                    meters.forEach((meter, index) => {
                        setTimeout(() => {
                            meter.style.transform = 'scaleX(1)';
                        }, index * 200);
                    });
                }
            });
        }, observerOptions);

        const tasteProfile = document.querySelector('.taste-profile');
        if (tasteProfile) {
            // Initially hide meters
            const meters = tasteProfile.querySelectorAll('.meter-fill');
            meters.forEach(meter => {
                meter.style.transform = 'scaleX(0)';
                meter.style.transformOrigin = 'left';
                meter.style.transition = 'transform 0.8s ease';
            });

            observer.observe(tasteProfile);
        }
    });

    // Collapse/expand functionality for sections
    function toggleSection(sectionType) {
        const section = document.querySelector(`.${sectionType === 'discovery' ? 'drink-discovery' : 'smart-recommendations'}`);
        const icon = section.querySelector('.collapse-icon');

        section.classList.toggle('collapsed');

        if (section.classList.contains('collapsed')) {
            icon.textContent = '+';
        } else {
            icon.textContent = '−';
        }
    }
</script>

</div> <!-- End page-container -->

{% endblock %}