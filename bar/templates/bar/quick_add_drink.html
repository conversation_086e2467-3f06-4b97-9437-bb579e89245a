{% extends 'bar/base.html' %}

{% block title %}Hurtig Drink Tilføjelse{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Hero Section -->
            <div class="quick-add-hero text-center mb-4">
                <h1 class="display-5">⚡ Hurtig Drink Tilføjelse</h1>
                <p class="lead">Kopier data fra Shake-it og få det automatisk struktureret!</p>
            </div>

            <form id="quickAddForm" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="row">
                    <!-- Left Column - Basic Info -->
                    <div class="col-md-6">
                        <div class="card shadow mb-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">📝 Grundlæggende Info</h4>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="drinkName" class="form-label">Drink Navn:</label>
                                    <input type="text" class="form-control" id="drinkName" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="drinkType" class="form-label">Type:</label>
                                    <select class="form-select" id="drinkType">
                                        <option value="drink">🍹 Drink</option>
                                        <option value="cocktail">🍸 Cocktail</option>
                                        <option value="shot">🔫 Shot</option>
                                        <option value="nonalcoholic">🚫 Alkoholfri</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Beskrivelse:</label>
                                    <textarea class="form-control" id="description" rows="3" 
                                              placeholder="Kopier beskrivelse fra Shake-it..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="card shadow mb-4">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0">🖼️ Billede</h4>
                            </div>
                            <div class="card-body">
                                <div class="image-upload-area" id="imageUploadArea">
                                    <div class="upload-placeholder">
                                        <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
                                        <p>Træk billede hertil eller klik for at vælge</p>
                                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                                    </div>
                                    <div class="image-preview d-none" id="imagePreview">
                                        <img id="previewImg" src="" alt="Preview" class="img-fluid rounded">
                                        <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeImage()">
                                            Fjern billede
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Ingredients & Instructions -->
                    <div class="col-md-6">
                        <div class="card shadow mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h4 class="mb-0">🧪 Smart Ingrediens Parser</h4>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="ingredientsText" class="form-label">
                                        Ingredienser (kopier fra Shake-it):
                                    </label>
                                    <textarea class="form-control" id="ingredientsText" rows="8" 
                                              placeholder="Eksempel:
4 cl vodka
2 cl lime juice
1 tsk sukker
Salt til rim
Lime skive til pynt"></textarea>
                                    <div class="form-text">
                                        Indsæt ingredienser som de står på Shake-it - systemet parser dem automatisk!
                                    </div>
                                </div>
                                
                                <button type="button" class="btn btn-info" onclick="parseIngredients()">
                                    🔍 Parse Ingredienser
                                </button>
                            </div>
                        </div>

                        <div class="card shadow mb-4">
                            <div class="card-header bg-info text-white">
                                <h4 class="mb-0">📋 Fremgangsmåde</h4>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" id="instructions" rows="6" 
                                          placeholder="Kopier fremgangsmåde fra Shake-it..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parsed Ingredients Display -->
                <div class="card shadow mb-4 d-none" id="parsedIngredientsCard">
                    <div class="card-header bg-secondary text-white">
                        <h4 class="mb-0">✅ Parsede Ingredienser</h4>
                    </div>
                    <div class="card-body">
                        <div class="row" id="parsedIngredientsList">
                            <!-- Parsed ingredients will appear here -->
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                ✅ = Påkrævet ingrediens | ⚪ = Valgfri ingrediens
                                <br>Klik på en ingrediens for at ændre status
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg">
                        🍹 Tilføj Drink
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image upload handling
    const uploadArea = document.getElementById('imageUploadArea');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = uploadArea.querySelector('.upload-placeholder');

    // Click to upload
    uploadArea.addEventListener('click', () => imageInput.click());

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('drag-over');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('drag-over');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files[0]);
        }
    });

    imageInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleImageUpload(e.target.files[0]);
        }
    });

    function handleImageUpload(file) {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('previewImg').src = e.target.result;
                uploadPlaceholder.classList.add('d-none');
                imagePreview.classList.remove('d-none');
            };
            reader.readAsDataURL(file);
        }
    }

    // Form submission
    document.getElementById('quickAddForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitDrink();
    });
});

function removeImage() {
    document.getElementById('imageInput').value = '';
    document.getElementById('imagePreview').classList.add('d-none');
    document.querySelector('.upload-placeholder').classList.remove('d-none');
}

function parseIngredients() {
    const text = document.getElementById('ingredientsText').value;
    if (!text.trim()) {
        Swal.fire('Fejl', 'Indtast ingredienser først!', 'warning');
        return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const ingredients = [];

    lines.forEach(line => {
        const parsed = parseIngredientLine(line.trim());
        if (parsed) {
            ingredients.push(parsed);
        }
    });

    displayParsedIngredients(ingredients);
}

function parseIngredientLine(line) {
    // Regex patterns for different formats
    const patterns = [
        /^(\d+(?:[.,]\d+)?)\s*(cl|ml|oz|tsk|spsk|stk|dråber|splash)\s+(.+)$/i,
        /^(\d+(?:[.,]\d+)?)\s+(.+)$/i,
        /^(.+?)\s+til\s+(rim|kant|pynt|garnish)$/i,
        /^(.+)$/i
    ];

    for (let pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
            let amount, unit, name;
            
            if (match.length === 4) {
                // Amount + unit + name
                amount = parseFloat(match[1].replace(',', '.'));
                unit = match[2].toLowerCase();
                name = match[3];
            } else if (match.length === 3) {
                // Amount + name (no unit)
                amount = parseFloat(match[1].replace(',', '.'));
                unit = 'stk';
                name = match[2];
            } else {
                // Just name
                amount = null;
                unit = 'efter smag';
                name = match[1];
            }

            // Determine if required or optional
            const required = isIngredientRequired(name);

            return {
                name: name.trim(),
                amount: amount,
                unit: unit,
                required: required,
                original: line
            };
        }
    }

    return null;
}

function isIngredientRequired(name) {
    const nameLower = name.toLowerCase();
    
    // Optional keywords
    const optionalKeywords = [
        'salt', 'sukker', 'lime', 'citron', 'appelsin', 'pynt', 'garnish',
        'rim', 'kant', 'decoration', 'skive', 'twist', 'til pynt', 'til rim'
    ];
    
    // Required keywords (spirits, main ingredients)
    const requiredKeywords = [
        'vodka', 'gin', 'rum', 'whiskey', 'tequila', 'bourbon', 'cognac',
        'mynte', 'mint', 'basilikum', 'juice', 'sirup', 'likør'
    ];

    // Check required first
    for (let keyword of requiredKeywords) {
        if (nameLower.includes(keyword)) {
            return true;
        }
    }

    // Check optional
    for (let keyword of optionalKeywords) {
        if (nameLower.includes(keyword)) {
            return false;
        }
    }

    // Default to required for main ingredients
    return true;
}

function displayParsedIngredients(ingredients) {
    const container = document.getElementById('parsedIngredientsList');
    const card = document.getElementById('parsedIngredientsCard');
    
    container.innerHTML = '';
    
    ingredients.forEach((ing, index) => {
        const col = document.createElement('div');
        col.className = 'col-md-6 mb-2';
        
        const requiredIcon = ing.required ? '✅' : '⚪';
        const amountText = ing.amount ? `${ing.amount} ${ing.unit}` : ing.unit;
        
        col.innerHTML = `
            <div class="ingredient-item p-2 border rounded cursor-pointer" 
                 onclick="toggleRequired(${index})" 
                 data-index="${index}">
                <span class="required-icon">${requiredIcon}</span>
                <strong>${ing.name}</strong>
                <small class="text-muted d-block">${amountText}</small>
            </div>
        `;
        
        container.appendChild(col);
    });
    
    card.classList.remove('d-none');
    
    // Store ingredients globally for form submission
    window.parsedIngredients = ingredients;
}

function toggleRequired(index) {
    if (window.parsedIngredients && window.parsedIngredients[index]) {
        window.parsedIngredients[index].required = !window.parsedIngredients[index].required;
        
        const item = document.querySelector(`[data-index="${index}"]`);
        const icon = item.querySelector('.required-icon');
        icon.textContent = window.parsedIngredients[index].required ? '✅' : '⚪';
    }
}

function submitDrink() {
    const formData = new FormData();
    
    // Basic info
    formData.append('name', document.getElementById('drinkName').value);
    formData.append('drink_type', document.getElementById('drinkType').value);
    formData.append('description', document.getElementById('description').value);
    formData.append('instructions', document.getElementById('instructions').value);
    
    // Image
    const imageFile = document.getElementById('imageInput').files[0];
    if (imageFile) {
        formData.append('image', imageFile);
    }
    
    // Ingredients
    if (window.parsedIngredients) {
        formData.append('ingredients', JSON.stringify(window.parsedIngredients));
    }
    
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    // Submit
    fetch('/api/quick-add-drink/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                title: 'Succes!',
                text: `Drink "${data.drink_name}" er tilføjet!`,
                icon: 'success',
                confirmButtonText: 'Se drink'
            }).then(() => {
                window.location.href = `/drinks/${data.drink_id}/`;
            });
        } else {
            Swal.fire('Fejl!', data.error, 'error');
        }
    })
    .catch(error => {
        Swal.fire('Fejl!', 'Netværksfejl: ' + error.message, 'error');
    });
}
</script>

<style>
.quick-add-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
}

.image-upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-upload-area:hover,
.image-upload-area.drag-over {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

.ingredient-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.ingredient-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.required-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.card {
    border: none;
    border-radius: 15px;
}

.btn {
    border-radius: 10px;
}
</style>
{% endblock %}
