{% extends 'bar/base.html' %}

{% block title %}Fest Planlægning{% endblock %}

{% block content %}
<style>
    .planner-card {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        background: #f8f9fa;
    }
    
    .drink-selector {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    
    .drink-selector.selected {
        border-color: #0d6efd;
        background: #e7f3ff;
    }
    
    .servings-input {
        width: 80px;
        display: inline-block;
    }
    
    .plan-summary {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
    
    .existing-plan {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    
    body.dark-mode .planner-card {
        background: #2b2b2b;
        border-color: #444;
    }
    
    body.dark-mode .drink-selector {
        background: #1f1f1f;
        border-color: #444;
        color: #eee;
    }
    
    body.dark-mode .drink-selector.selected {
        background: #1a3a5c;
        border-color: #90CAF9;
    }
    
    body.dark-mode .plan-summary {
        background: #2b3e50;
        border-color: #34495e;
        color: #eee;
    }
    
    body.dark-mode .existing-plan {
        background: #1f1f1f;
        border-color: #444;
        color: #eee;
    }
</style>

<h1 class="mb-4">🧮 Ingrediens Beregner</h1>

<div class="row">
    <div class="col-lg-8">
        <div class="planner-card">
            <h3>Beregn ingrediens-mængder</h3>
            <p class="text-muted">Vælg drinks og antal portioner - få en komplet indkøbsliste med præcise mængder</p>
            
            <form id="ingredientCalculator">
                {% csrf_token %}

                <h4 class="mb-3">Vælg drinks og antal portioner (du kan lave {{ can_make_drinks|length }} forskellige)</h4>
                
                {% if can_make_drinks %}
                    <div id="drinkSelectors">
                        {% for drink in can_make_drinks %}
                        <div class="drink-selector" data-drink-id="{{ drink.id }}">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input drink-checkbox" type="checkbox"
                                               value="{{ drink.id }}" id="drink_{{ drink.id }}">
                                        <label class="form-check-label" for="drink_{{ drink.id }}">
                                            <strong>{{ drink.name }}</strong>
                                            <small class="text-muted d-block">{{ drink.get_drink_type_display }}</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <label class="form-label me-2 mb-0">Antal:</label>
                                        <input type="number" class="form-control servings-input"
                                               name="servings_{{ drink.id }}" min="1" max="50" step="1" value="1">
                                        <span class="ms-2 text-muted">stk</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="plan-summary" id="ingredientSummary" style="display: none;">
                        <h5>🛒 Indkøbsliste</h5>
                        <div id="totalIngredientsContent"></div>
                        <h6 class="mt-3">📋 Drink detaljer</h6>
                        <div id="drinkDetailsContent"></div>
                        <button type="button" class="btn btn-primary mt-3" onclick="calculateIngredients()">🔄 Genberegn</button>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <strong>Ingen drinks tilgængelige!</strong><br>
                        Du skal først <a href="{% url 'my_bar' %}">tilføje ingredienser til din bar</a> 
                        for at kunne planlægge drinks.
                    </div>
                {% endif %}
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="planner-card">
            <h4>💡 Sådan bruger du beregneren</h4>
            <ol class="list-unstyled">
                <li class="mb-2">1️⃣ <strong>Tilføj ingredienser:</strong> Gå til admin og tilføj mængder til dine drinks</li>
                <li class="mb-2">2️⃣ <strong>Vælg drinks:</strong> Klik på de drinks du vil lave</li>
                <li class="mb-2">3️⃣ <strong>Angiv antal:</strong> Hvor mange af hver drink?</li>
                <li class="mb-2">4️⃣ <strong>Få indkøbsliste:</strong> Se præcis hvor meget du skal bruge</li>
            </ol>

            <div class="alert alert-warning mt-3">
                <small><strong>Vigtigt:</strong> Du skal først tilføje ingrediens-mængder til dine drinks i admin-panelet!</small>
            </div>
        </div>
        
        {% if user_plans %}
        <div class="planner-card">
            <h4>📅 Dine tidligere planer</h4>
            {% for plan in user_plans %}
            <div class="existing-plan">
                <h6>{{ plan.name }}</h6>
                <small class="text-muted">{{ plan.guest_count }} gæster • {{ plan.created_at|date:"d/m/Y" }}</small>
                <div class="mt-2">
                    {% for item in plan.items.all %}
                        <small class="d-block">• {{ item.drink.name }} ({{ item.total_servings }} stk)</small>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>

<script>
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const csrftoken = getCookie('csrftoken');
    
    // Update calculations when inputs change
    document.addEventListener('input', updateDisplay);
    document.addEventListener('change', updateDisplay);

    function updateDisplay() {
        const selectedDrinks = [];

        document.querySelectorAll('.drink-checkbox:checked').forEach(checkbox => {
            const drinkId = checkbox.value;
            const servingsInput = document.querySelector(`input[name="servings_${drinkId}"]`);
            const servings = parseInt(servingsInput.value) || 0;

            selectedDrinks.push({
                id: drinkId,
                name: checkbox.closest('.drink-selector').querySelector('strong').textContent,
                servings: servings
            });
        });

        // Update summary visibility
        const summary = document.getElementById('ingredientSummary');
        if (selectedDrinks.length > 0) {
            summary.style.display = 'block';
            calculateIngredients();
        } else {
            summary.style.display = 'none';
        }

        // Update selector styling
        document.querySelectorAll('.drink-selector').forEach(selector => {
            const checkbox = selector.querySelector('.drink-checkbox');
            selector.classList.toggle('selected', checkbox.checked);
        });
    }

    function calculateIngredients() {
        const selectedDrinks = [];

        document.querySelectorAll('.drink-checkbox:checked').forEach(checkbox => {
            const drinkId = checkbox.value;
            const servingsInput = document.querySelector(`input[name="servings_${drinkId}"]`);
            const servings = parseInt(servingsInput.value) || 0;

            if (servings > 0) {
                selectedDrinks.push(`${drinkId}:${servings}`);
            }
        });

        if (selectedDrinks.length === 0) {
            return;
        }

        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', csrftoken);
        selectedDrinks.forEach(drink => {
            formData.append('selected_drinks', drink);
        });

        fetch('{% url "calculate_ingredients" %}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                displayIngredientResults(data);
            } else {
                alert('Fejl: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Der opstod en fejl ved beregning af ingredienser.');
        });
    }

    function displayIngredientResults(data) {
        // Display total ingredients (shopping list)
        const totalContent = document.getElementById('totalIngredientsContent');
        let totalHtml = '<div class="alert alert-success"><h6>🛒 Total indkøbsliste:</h6><ul class="mb-0">';
        data.total_ingredients.forEach(ingredient => {
            totalHtml += `<li><strong>${ingredient.name}:</strong> ${ingredient.total_amount} ${ingredient.unit}</li>`;
        });
        totalHtml += '</ul></div>';
        totalContent.innerHTML = totalHtml;

        // Display drink details
        const detailsContent = document.getElementById('drinkDetailsContent');
        let detailsHtml = '';
        data.drink_details.forEach(drink => {
            detailsHtml += `<div class="mb-3">
                <h6>${drink.name} (${drink.servings} stk)</h6>
                <ul class="small text-muted">`;
            drink.ingredients.forEach(ing => {
                detailsHtml += `<li>${ing.name}: ${ing.amount_per_serving} ${ing.unit} × ${drink.servings} = ${ing.total_amount} ${ing.unit}</li>`;
            });
            detailsHtml += '</ul></div>';
        });
        detailsContent.innerHTML = detailsHtml;
    }
    
    // Initial display update
    updateDisplay();
</script>

{% endblock %}
