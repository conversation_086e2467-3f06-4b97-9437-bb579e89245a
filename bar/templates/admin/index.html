{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block extrahead %}
{{ block.super }}
<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
}

.stat-card:nth-child(1)::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.stat-card:nth-child(2)::before { background: linear-gradient(90deg, #f093fb, #f5576c); }
.stat-card:nth-child(3)::before { background: linear-gradient(90deg, #4facfe, #00f2fe); }
.stat-card:nth-child(4)::before { background: linear-gradient(90deg, #43e97b, #38f9d7); }

.stat-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.stat-number {
    font-size: 36px;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.welcome-hero {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    margin-bottom: 40px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.welcome-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.welcome-hero::after {
    content: '🍹✨🥃🍸🍷';
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 24px;
    animation: bounce 3s ease-in-out infinite;
    letter-spacing: 5px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.welcome-hero h1 {
    font-size: 42px;
    font-weight: 800;
    margin-bottom: 15px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.welcome-hero p {
    font-size: 18px;
    opacity: 0.9;
    margin-bottom: 0;
    font-weight: 500;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 40px;
}

.quick-action {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.quick-action:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    text-decoration: none;
    color: #667eea;
}

.quick-action-icon {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.quick-action-label {
    font-weight: 600;
    font-size: 14px;
}

/* Enhanced animations */
.stat-icon {
    animation: bounce 2s infinite;
}

.stat-card:hover .stat-icon {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Time-based greeting */
.time-greeting {
    font-size: 16px;
    opacity: 0.8;
    margin-top: 10px;
    font-weight: 500;
}

/* Progress bars for stats */
.stat-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-top: 10px;
    overflow: hidden;
}

.stat-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
    border-radius: 2px;
    animation: progressLoad 2s ease-out;
}

@keyframes progressLoad {
    from { width: 0%; }
    to { width: var(--progress-width, 75%); }
}
</style>
{% endblock %}

{% block content %}
<div class="welcome-hero">
    <h1>🍹 Velkommen til MyBar Admin</h1>
    <p>Administrer drinks, ingredienser og meget mere fra dit kontrolpanel</p>
    <div class="time-greeting" id="timeGreeting"></div>
</div>

<script>
// Dynamic time-based greeting
function updateGreeting() {
    const hour = new Date().getHours();
    const greetingElement = document.getElementById('timeGreeting');

    if (hour < 12) {
        greetingElement.textContent = '☀️ God morgen! Klar til at skabe fantastiske drinks?';
    } else if (hour < 17) {
        greetingElement.textContent = '🌤️ God eftermiddag! Lad os arbejde på din bar!';
    } else {
        greetingElement.textContent = '🌙 God aften! Tid til at perfektionere dine cocktails!';
    }
}

// Update greeting on page load
updateGreeting();

// Real-time clock
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('da-DK');
    const dateString = now.toLocaleDateString('da-DK', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    let clockElement = document.getElementById('realTimeClock');
    if (!clockElement) {
        clockElement = document.createElement('div');
        clockElement.id = 'realTimeClock';
        clockElement.style.cssText = `
            position: fixed;
            top: 80px;
            right: 80px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: #2c3e50;
            z-index: 1000;
            text-align: center;
            min-width: 200px;
        `;
        document.body.appendChild(clockElement);
    }

    clockElement.innerHTML = `
        <div style="font-size: 18px; margin-bottom: 5px;">${timeString}</div>
        <div style="font-size: 12px; opacity: 0.7;">${dateString}</div>
    `;
}

// Update clock every second
setInterval(updateClock, 1000);
updateClock();

// Animated counter for stats
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    });
}

// Start counter animation when page loads
setTimeout(animateCounters, 500);

// Add weather widget (mock data)
function addWeatherWidget() {
    const weather = document.createElement('div');
    weather.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 15px 20px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        z-index: 1000;
        text-align: center;
        min-width: 150px;
    `;

    const weatherEmojis = ['☀️', '⛅', '🌤️', '🌦️', '⛈️'];
    const randomWeather = weatherEmojis[Math.floor(Math.random() * weatherEmojis.length)];
    const randomTemp = Math.floor(Math.random() * 15) + 10;

    weather.innerHTML = `
        <div style="font-size: 24px; margin-bottom: 5px;">${randomWeather}</div>
        <div style="font-size: 16px;">${randomTemp}°C</div>
        <div style="font-size: 12px; opacity: 0.8;">København</div>
    `;

    document.body.appendChild(weather);
}

// Add weather widget
addWeatherWidget();

// Add recent activity widget
function addRecentActivity() {
    const activity = document.createElement('div');
    activity.style.cssText = `
        position: fixed;
        top: 150px;
        right: 20px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        font-family: 'Inter', sans-serif;
        z-index: 1000;
        width: 280px;
        max-height: 300px;
        overflow-y: auto;
    `;

    const activities = [
        { icon: '🍹', text: 'New drink "Mojito Supreme" added', time: '2 min ago' },
        { icon: '🥃', text: 'Ingredient "Premium Vodka" updated', time: '5 min ago' },
        { icon: '👤', text: 'User "bartender123" registered', time: '10 min ago' },
        { icon: '⭐', text: 'Drink "Old Fashioned" rated 5 stars', time: '15 min ago' },
        { icon: '🏷️', text: 'New group "Whiskey Collection" created', time: '20 min ago' }
    ];

    let activityHTML = `
        <div style="font-weight: 700; margin-bottom: 15px; color: #2c3e50; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
            📊 Recent Activity
        </div>
    `;

    activities.forEach(item => {
        activityHTML += `
            <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 8px; border-radius: 8px; background: rgba(102, 126, 234, 0.05); transition: all 0.3s ease;"
                 onmouseover="this.style.background='rgba(102, 126, 234, 0.1)'"
                 onmouseout="this.style.background='rgba(102, 126, 234, 0.05)'">
                <span style="font-size: 20px; margin-right: 10px;">${item.icon}</span>
                <div style="flex: 1;">
                    <div style="font-size: 13px; color: #2c3e50; font-weight: 500;">${item.text}</div>
                    <div style="font-size: 11px; color: #666; margin-top: 2px;">${item.time}</div>
                </div>
            </div>
        `;
    });

    activity.innerHTML = activityHTML;
    document.body.appendChild(activity);
}

// Add activity widget
addRecentActivity();

// Add floating action button
function addFloatingActionButton() {
    const fab = document.createElement('div');
    fab.style.cssText = `
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        z-index: 1001;
    `;

    fab.innerHTML = '➕';
    fab.title = 'Quick Add';

    fab.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1) rotate(90deg)';
        this.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
    });

    fab.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1) rotate(0deg)';
        this.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
    });

    fab.addEventListener('click', function() {
        const menu = document.createElement('div');
        menu.style.cssText = `
            position: fixed;
            bottom: 150px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1002;
            min-width: 200px;
        `;

        menu.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 10px; color: #2c3e50;">Quick Actions</div>
            <a href="/admin/bar/drink/add/" style="display: block; padding: 8px 12px; margin: 5px 0; text-decoration: none; color: #667eea; border-radius: 8px; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(102, 126, 234, 0.1)'" onmouseout="this.style.background='transparent'">🍹 Add Drink</a>
            <a href="/admin/bar/ingredient/add/" style="display: block; padding: 8px 12px; margin: 5px 0; text-decoration: none; color: #667eea; border-radius: 8px; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(102, 126, 234, 0.1)'" onmouseout="this.style.background='transparent'">🥃 Add Ingredient</a>
            <a href="/admin/bar/ingredientgroup/add/" style="display: block; padding: 8px 12px; margin: 5px 0; text-decoration: none; color: #667eea; border-radius: 8px; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(102, 126, 234, 0.1)'" onmouseout="this.style.background='transparent'">🏷️ Add Group</a>
        `;

        document.body.appendChild(menu);

        setTimeout(() => {
            document.addEventListener('click', function removeMenu(e) {
                if (!menu.contains(e.target) && e.target !== fab) {
                    document.body.removeChild(menu);
                    document.removeEventListener('click', removeMenu);
                }
            });
        }, 100);
    });

    document.body.appendChild(fab);
}

// Add floating action button
addFloatingActionButton();
</script>

<div class="stats-grid">
    <div class="stat-card">
        <span class="stat-icon">🍹</span>
        <div class="stat-number">{{ total_drinks|default:"0" }}</div>
        <div class="stat-label">Drinks Total</div>
        <div class="stat-progress">
            <div class="stat-progress-bar" style="--progress-width: 85%;"></div>
        </div>
    </div>
    <div class="stat-card">
        <span class="stat-icon">🥃</span>
        <div class="stat-number">{{ total_ingredients|default:"0" }}</div>
        <div class="stat-label">Ingredienser</div>
        <div class="stat-progress">
            <div class="stat-progress-bar" style="--progress-width: 70%;"></div>
        </div>
    </div>
    <div class="stat-card">
        <span class="stat-icon">👥</span>
        <div class="stat-number">{{ total_users|default:"0" }}</div>
        <div class="stat-label">Brugere</div>
        <div class="stat-progress">
            <div class="stat-progress-bar" style="--progress-width: 60%;"></div>
        </div>
    </div>
    <div class="stat-card">
        <span class="stat-icon">⭐</span>
        <div class="stat-number">{{ total_ratings|default:"0" }}</div>
        <div class="stat-label">Bedømmelser</div>
        <div class="stat-progress">
            <div class="stat-progress-bar" style="--progress-width: 90%;"></div>
        </div>
    </div>
</div>

<div class="quick-actions">
    <a href="{% url 'admin:bar_drink_add' %}" class="quick-action">
        <span class="quick-action-icon">🍹</span>
        <div class="quick-action-label">Tilføj Drink</div>
    </a>
    <a href="{% url 'admin:bar_ingredient_add' %}" class="quick-action">
        <span class="quick-action-icon">🥃</span>
        <div class="quick-action-label">Tilføj Ingrediens</div>
    </a>
    <a href="{% url 'admin:bar_ingredientgroup_add' %}" class="quick-action">
        <span class="quick-action-icon">🏷️</span>
        <div class="quick-action-label">Tilføj Gruppe</div>
    </a>
    <a href="{% url 'drink_list' %}" class="quick-action" target="_blank">
        <span class="quick-action-icon">🌐</span>
        <div class="quick-action-label">Se Hjemmeside</div>
    </a>
</div>

{% if app_list %}
    <div class="dashboard">
        {% for app in app_list %}
            <div class="app-{{ app.app_label }} module">
                <table>
                    <caption>
                        <a href="{{ app.app_url }}" class="section" title="{% blocktranslate with name=app.name %}Models in the {{ name }} application{% endblocktranslate %}">{{ app.name }}</a>
                    </caption>
                    {% for model in app.models %}
                        <tr class="model-{{ model.object_name|lower }}">
                            {% if model.admin_url %}
                                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                            {% else %}
                                <th scope="row">{{ model.name }}</th>
                            {% endif %}

                            {% if model.add_url %}
                                <td><a href="{{ model.add_url }}" class="addlink">{% translate 'Add' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}

                            {% if model.admin_url %}
                                {% if model.view_only %}
                                    <td><a href="{{ model.admin_url }}" class="viewlink">{% translate 'View' %}</a></td>
                                {% else %}
                                    <td><a href="{{ model.admin_url }}" class="changelink">{% translate 'Change' %}</a></td>
                                {% endif %}
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% endfor %}
    </div>
{% else %}
    <p>{% translate "You don't have permission to view or edit anything." %}</p>
{% endif %}
{% endblock %}
