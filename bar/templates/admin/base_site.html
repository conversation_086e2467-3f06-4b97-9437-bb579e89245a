{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | 🍹 MyBar Admin{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        🍹 MyBar Admin
    </a>
</h1>
{% endblock %}

{% block extrahead %}
{{ block.super }}
<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🍹</text></svg>">
<style>
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global Admin Styling */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}

/* Header Styling */
#header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

#branding h1 {
    color: #2c3e50 !important;
    font-weight: 700 !important;
    font-size: 28px !important;
    text-shadow: none !important;
}

#branding h1 a:link, #branding h1 a:visited {
    color: #2c3e50 !important;
}

/* Main Content Area */
#content {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    margin: 20px !important;
    padding: 30px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Module Headers */
.module h2, .module caption, .inline-group h2 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 20px !important;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    margin: 0 !important;
    text-shadow: none !important;
    border: none !important;
}

/* Buttons */
.default, input[type="submit"], .submit-row input, .button {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.default:hover, input[type="submit"]:hover,
.submit-row input:hover, .button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* Dashboard Layout Fix */
.dashboard {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
    gap: 25px !important;
    margin-top: 30px !important;
}

/* Dashboard Styling */
.dashboard .module {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9)) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    overflow: hidden !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    position: relative !important;
    min-height: 200px !important;
    width: 100% !important;
    margin-bottom: 0 !important;
}

.dashboard .module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c) !important;
    background-size: 300% 100% !important;
    animation: gradientShift 3s ease infinite !important;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.dashboard .module:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2) !important;
}

/* Dashboard Module Content */
.dashboard .module h2 {
    background: transparent !important;
    color: #2c3e50 !important;
    padding: 20px 25px 15px !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    margin: 0 !important;
    position: relative !important;
    border-radius: 0 !important;
}

.dashboard .module h2::before {
    content: '';
    position: absolute;
    left: 25px;
    bottom: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2) !important;
    border-radius: 2px !important;
}

.dashboard .module table {
    margin: 0 !important;
    border: none !important;
    width: 100% !important;
}

.dashboard .module td {
    padding: 15px 25px !important;
    border: none !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.dashboard .module td:last-child {
    border-bottom: none !important;
}

.dashboard .module a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    padding: 8px 0 !important;
    font-size: 14px !important;
}

.dashboard .module a:hover {
    color: #764ba2 !important;
    transform: translateX(5px) !important;
}

/* Sidebar Navigation */
#nav-sidebar {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95)) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 0 20px 20px 0 !important;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 20px 0 !important;
}

#nav-sidebar .app-bar .app-label {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 20px !important;
    margin: 0 15px 10px !important;
    border-radius: 12px !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

#nav-sidebar .model-link {
    display: block !important;
    padding: 12px 25px !important;
    margin: 5px 15px !important;
    color: #2c3e50 !important;
    text-decoration: none !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
}

#nav-sidebar .model-link:hover {
    color: #667eea !important;
    transform: translateX(8px) !important;
    background: rgba(102, 126, 234, 0.1) !important;
}

/* Add icons to navigation */
#nav-sidebar a[href*="ingredient/"]:before { content: "🥃 "; margin-right: 8px; }
#nav-sidebar a[href*="drink/"]:before { content: "🍹 "; margin-right: 8px; }
#nav-sidebar a[href*="ingredientgroup/"]:before { content: "🏷️ "; margin-right: 8px; }
#nav-sidebar a[href*="userbar/"]:before { content: "👤 "; margin-right: 8px; }
#nav-sidebar a[href*="favorite/"]:before { content: "❤️ "; margin-right: 8px; }
#nav-sidebar a[href*="rating/"]:before { content: "⭐ "; margin-right: 8px; }
#nav-sidebar a[href*="comment/"]:before { content: "💬 "; margin-right: 8px; }
#nav-sidebar a[href*="drinkplan/"]:before { content: "🎉 "; margin-right: 8px; }

/* Add icons to dashboard links */
.dashboard .module a[href*="ingredient/"]:before { content: "🥃 "; }
.dashboard .module a[href*="drink/"]:before { content: "🍹 "; }
.dashboard .module a[href*="ingredientgroup/"]:before { content: "🏷️ "; }
.dashboard .module a[href*="userbar/"]:before { content: "👤 "; }
.dashboard .module a[href*="favorite/"]:before { content: "❤️ "; }
.dashboard .module a[href*="rating/"]:before { content: "⭐ "; }
.dashboard .module a[href*="comment/"]:before { content: "💬 "; }
.dashboard .module a[href*="drinkplan/"]:before { content: "🎉 "; }

/* Form Styling */
.form-row {
    background: rgba(248, 249, 250, 0.8) !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin-bottom: 15px !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
}

.form-row:hover {
    background: rgba(248, 249, 250, 1) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
}

/* Input Fields */
input[type="text"], input[type="email"], input[type="password"],
input[type="number"], textarea, select {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: white !important;
}

input[type="text"]:focus, input[type="email"]:focus,
input[type="password"]:focus, input[type="number"]:focus,
textarea:focus, select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* Tables */
table {
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
}

thead th {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 15px !important;
    border: none !important;
}

tbody tr {
    transition: all 0.3s ease !important;
}

tbody tr:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

tbody td {
    padding: 15px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Fieldsets */
fieldset {
    background: white !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    padding: 0 !important;
}

fieldset h2 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    margin: 0 !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    border-radius: 15px 15px 0 0 !important;
}

/* Inline Forms */
.inline-group {
    background: white !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Success Messages */
.messagelist .success {
    background: linear-gradient(135deg, #2ecc71, #27ae60) !important;
    color: white !important;
    border-radius: 10px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3) !important;
}

/* Error Messages */
.messagelist .error {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    color: white !important;
    border-radius: 10px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 25px !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.breadcrumbs a {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

.breadcrumbs a:hover {
    color: white !important;
}

/* Enhanced Visual Effects */
/* Floating particles background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Enhanced Navigation with Icons */
#nav-sidebar .app-bar .app-label::after {
    content: '📊';
    float: right;
    font-size: 18px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* Enhanced Tables with Zebra Stripes */
.results tbody tr:nth-child(even) {
    background: rgba(102, 126, 234, 0.02) !important;
}

.results tbody tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.9) !important;
}

/* Action Row Styling */
.actions {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9)) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
}

.actions::before {
    content: '⚡';
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Enhanced Search Box */
#searchbar {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9)) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
}

#searchbar::before {
    content: '🔍';
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 20px;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Pagination */
.paginator {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9)) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-top: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-align: center !important;
}

.paginator a, .paginator .this-page {
    display: inline-block !important;
    padding: 10px 15px !important;
    margin: 0 5px !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-weight: 600 !important;
    position: relative !important;
    overflow: hidden !important;
}

.paginator a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.paginator a:hover::before {
    left: 100%;
}

.paginator a {
    background: #f8f9fa !important;
    color: #667eea !important;
    border: 2px solid #e9ecef !important;
}

.paginator a:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.paginator .this-page {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: 2px solid transparent !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

/* Enhanced Filter Sidebar */
#changelist-filter {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95)) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 0 !important;
    overflow: hidden !important;
    position: relative !important;
}

#changelist-filter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb) !important;
    background-size: 200% 100% !important;
    animation: gradientShift 2s ease infinite !important;
}

#changelist-filter h3 {
    background: transparent !important;
    color: #2c3e50 !important;
    padding: 15px 20px !important;
    margin: 0 !important;
    font-weight: 700 !important;
    position: relative !important;
    font-size: 16px !important;
}

#changelist-filter h3::after {
    content: '🔍';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    animation: pulse 2s infinite;
}

#changelist-filter ul {
    padding: 15px 0 !important;
    margin: 0 !important;
}

#changelist-filter li {
    padding: 8px 20px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

#changelist-filter li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #667eea, #764ba2) !important;
    transform: scaleY(0) !important;
    transition: transform 0.3s ease !important;
}

#changelist-filter li:hover::before {
    transform: scaleY(1) !important;
}

#changelist-filter li:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: translateX(5px) !important;
}

#changelist-filter a {
    color: #2c3e50 !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

#changelist-filter a:hover {
    color: #667eea !important;
}

/* Enhanced User Tools */
#user-tools {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    padding: 10px 20px !important;
    border-radius: 0 0 15px 15px !important;
}

#user-tools a {
    color: #2c3e50 !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

#user-tools a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2)) !important;
    transition: left 0.3s ease !important;
}

#user-tools a:hover::before {
    left: 0 !important;
}

#user-tools a:hover {
    color: #667eea !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 12px !important;
    border-radius: 50% !important;
    width: 50px !important;
    height: 50px !important;
    cursor: pointer !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    transition: all 0.3s ease !important;
    font-size: 20px !important;
}

.dark-mode-toggle:hover {
    transform: scale(1.1) rotate(180deg) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

/* Dark Mode Styles */
body.dark-mode {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
}

body.dark-mode #header {
    background: rgba(30, 30, 50, 0.95) !important;
}

body.dark-mode #content {
    background: rgba(30, 30, 50, 0.95) !important;
    color: #e0e0e0 !important;
}

body.dark-mode .dashboard .module {
    background: linear-gradient(135deg, rgba(40, 40, 60, 0.9), rgba(30, 30, 50, 0.9)) !important;
    color: #e0e0e0 !important;
}

body.dark-mode #nav-sidebar {
    background: linear-gradient(180deg, rgba(30, 30, 50, 0.95), rgba(20, 20, 40, 0.95)) !important;
}

body.dark-mode .form-row {
    background: rgba(40, 40, 60, 0.8) !important;
    color: #e0e0e0 !important;
}

body.dark-mode input, body.dark-mode textarea, body.dark-mode select {
    background: rgba(50, 50, 70, 0.9) !important;
    color: #e0e0e0 !important;
    border-color: #555 !important;
}

/* Enhanced Tooltips */
.admin-tooltip {
    position: relative !important;
    cursor: help !important;
}

.admin-tooltip::after {
    content: attr(data-tooltip) !important;
    position: absolute !important;
    bottom: 125% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    pointer-events: none !important;
}

.admin-tooltip:hover::after {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Enhanced Loading States */
.loading-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(102, 126, 234, 0.1) !important;
    backdrop-filter: blur(5px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9998 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.loading-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.loading-spinner {
    width: 60px !important;
    height: 60px !important;
    border: 4px solid rgba(255, 255, 255, 0.3) !important;
    border-top: 4px solid #667eea !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Notifications */
.admin-notification {
    position: fixed !important;
    top: 80px !important;
    right: 20px !important;
    background: linear-gradient(135deg, #2ecc71, #27ae60) !important;
    color: white !important;
    padding: 15px 20px !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3) !important;
    z-index: 9997 !important;
    transform: translateX(400px) !important;
    transition: all 0.3s ease !important;
    max-width: 300px !important;
}

.admin-notification.show {
    transform: translateX(0) !important;
}

.admin-notification.error {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
}

/* Enhanced Keyboard Shortcuts */
.keyboard-shortcuts {
    position: fixed !important;
    bottom: 20px !important;
    left: 20px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    font-size: 12px !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 9996 !important;
}

.keyboard-shortcuts.show {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }

    .dashboard .module {
        min-height: 150px !important;
    }

    #content {
        margin: 10px !important;
        padding: 20px !important;
        border-radius: 15px !important;
    }

    .form-row {
        padding: 15px !important;
    }

    #nav-sidebar {
        border-radius: 0 15px 15px 0 !important;
    }

    .dark-mode-toggle {
        top: 10px !important;
        right: 10px !important;
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }

    .admin-notification {
        right: 10px !important;
        max-width: calc(100vw - 40px) !important;
    }
}
</style>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block footer %}
{{ block.super }}

<!-- Dark Mode Toggle -->
<button class="dark-mode-toggle" id="darkModeToggle" title="Toggle Dark Mode">🌙</button>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

<!-- Keyboard Shortcuts Help -->
<div class="keyboard-shortcuts" id="keyboardShortcuts">
    <strong>Keyboard Shortcuts:</strong><br>
    Alt+D: Toggle Dark Mode<br>
    Alt+H: Show/Hide Help<br>
    Alt+S: Focus Search
</div>

<script>
// Dark Mode Toggle
document.addEventListener('DOMContentLoaded', function() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;
    const keyboardShortcuts = document.getElementById('keyboardShortcuts');

    // Load saved dark mode preference
    const isDarkMode = localStorage.getItem('adminDarkMode') === 'true';
    if (isDarkMode) {
        body.classList.add('dark-mode');
        darkModeToggle.textContent = '☀️';
    }

    // Dark mode toggle
    darkModeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');
        const isDark = body.classList.contains('dark-mode');
        darkModeToggle.textContent = isDark ? '☀️' : '🌙';
        localStorage.setItem('adminDarkMode', isDark);

        showNotification(isDark ? 'Dark mode enabled' : 'Light mode enabled');
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.altKey) {
            switch(e.key) {
                case 'd':
                case 'D':
                    e.preventDefault();
                    darkModeToggle.click();
                    break;
                case 'h':
                case 'H':
                    e.preventDefault();
                    keyboardShortcuts.classList.toggle('show');
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    const searchInput = document.querySelector('#searchbar input[type="text"]');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                    break;
            }
        }

        // Hide shortcuts on Escape
        if (e.key === 'Escape') {
            keyboardShortcuts.classList.remove('show');
        }
    });

    // Show loading overlay on form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            showLoading();
        });
    });

    // Enhanced hover effects for admin elements
    const adminElements = document.querySelectorAll('.module, .form-row, .inline-group');
    adminElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Auto-save draft functionality for textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const fieldName = textarea.name;
        if (fieldName) {
            // Load saved draft
            const savedDraft = localStorage.getItem(`draft_${fieldName}`);
            if (savedDraft && !textarea.value) {
                textarea.value = savedDraft;
                textarea.style.borderColor = '#f39c12';
                textarea.title = 'Draft restored';
            }

            // Save draft on input
            textarea.addEventListener('input', function() {
                localStorage.setItem(`draft_${fieldName}`, this.value);
                this.style.borderColor = '#f39c12';
            });

            // Clear draft on form submit
            const form = textarea.closest('form');
            if (form) {
                form.addEventListener('submit', function() {
                    localStorage.removeItem(`draft_${fieldName}`);
                });
            }
        }
    });

    // Enhanced table row highlighting
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // Remove previous selection
            tableRows.forEach(r => r.classList.remove('selected'));
            // Add selection to current row
            this.classList.add('selected');
        });
    });

    // Add tooltips to action buttons
    const actionButtons = document.querySelectorAll('.button, input[type="submit"]');
    actionButtons.forEach(button => {
        if (!button.title) {
            const text = button.textContent || button.value;
            button.title = `Click to ${text.toLowerCase()}`;
            button.classList.add('admin-tooltip');
            button.setAttribute('data-tooltip', button.title);
        }
    });
});

// Utility functions
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    overlay.classList.add('active');

    setTimeout(() => {
        overlay.classList.remove('active');
    }, 3000); // Auto-hide after 3 seconds
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `admin-notification ${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS for selected table rows
const style = document.createElement('style');
style.textContent = `
    tbody tr.selected {
        background: rgba(102, 126, 234, 0.2) !important;
        border-left: 4px solid #667eea !important;
    }

    .draft-indicator {
        position: relative;
    }

    .draft-indicator::after {
        content: '📝';
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 12px;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
