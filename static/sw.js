// MyBar Service Worker - PWA Offline Support
const CACHE_NAME = 'mybar-v1.0.0';
const STATIC_CACHE = 'mybar-static-v1';
const DYNAMIC_CACHE = 'mybar-dynamic-v1';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/my-bar/',
    '/drinks/',
    '/static/css/bootstrap.min.css',
    '/static/js/bootstrap.bundle.min.js',
    '/static/manifest.json',
    // Add more critical files
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 Caching static files...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('✅ Static files cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Failed to cache static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                if (cachedResponse) {
                    console.log('📦 Serving from cache:', request.url);
                    return cachedResponse;
                }
                
                // Not in cache, fetch from network
                return fetch(request)
                    .then(networkResponse => {
                        // Cache successful responses
                        if (networkResponse.status === 200) {
                            const responseClone = networkResponse.clone();
                            
                            caches.open(DYNAMIC_CACHE)
                                .then(cache => {
                                    cache.put(request, responseClone);
                                });
                        }
                        
                        return networkResponse;
                    })
                    .catch(error => {
                        console.log('🌐 Network failed, serving offline page');
                        
                        // Return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/offline/');
                        }
                        
                        // Return cached version or error
                        return caches.match(request);
                    });
            })
    );
});

// Background sync for when connection is restored
self.addEventListener('sync', event => {
    console.log('🔄 Background sync triggered:', event.tag);
    
    if (event.tag === 'bar-sync') {
        event.waitUntil(syncBarData());
    }
});

// Push notifications
self.addEventListener('push', event => {
    console.log('📱 Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'Du har en ny besked fra MyBar!',
        icon: '/static/icons/icon-192x192.png',
        badge: '/static/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/'
        },
        actions: [
            {
                action: 'open',
                title: 'Åbn MyBar',
                icon: '/static/icons/icon-96x96.png'
            },
            {
                action: 'close',
                title: 'Luk',
                icon: '/static/icons/icon-96x96.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('MyBar', options)
    );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
    console.log('📱 Notification clicked:', event.action);
    
    event.notification.close();
    
    if (event.action === 'open') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

// Sync bar data when online
async function syncBarData() {
    try {
        // Get pending changes from IndexedDB
        const pendingChanges = await getPendingChanges();
        
        for (const change of pendingChanges) {
            await fetch('/api/sync-bar/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(change)
            });
        }
        
        // Clear pending changes
        await clearPendingChanges();
        console.log('✅ Bar data synced successfully');
        
    } catch (error) {
        console.error('❌ Failed to sync bar data:', error);
    }
}

// Helper functions for IndexedDB (simplified)
async function getPendingChanges() {
    // Implementation for getting pending changes from IndexedDB
    return [];
}

async function clearPendingChanges() {
    // Implementation for clearing pending changes
}

console.log('🍹 MyBar Service Worker loaded successfully!');
