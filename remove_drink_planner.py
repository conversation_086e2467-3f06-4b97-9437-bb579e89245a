#!/usr/bin/env python3
"""
<PERSON>ript to completely remove the Drink Planner feature from MyBar app.
Run this if you don't like the drink planning functionality.

Usage: python remove_drink_planner.py
"""

import os
import re

def remove_drink_planner():
    print("🗑️  Fjerner Drink Planner funktionalitet...")
    
    # 1. Remove navigation link from base.html
    print("1. Fjerner navigation link...")
    base_html_path = "bar/templates/bar/base.html"
    if os.path.exists(base_html_path):
        with open(base_html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove the drink planner nav item
        content = re.sub(
            r'\s*<li class="nav-item">\s*<a class="nav-link" href="{% url \'drink_planner\' %}">🧮 Ingrediens Beregner</a>\s*</li>',
            '',
            content,
            flags=re.MULTILINE
        )
        
        with open(base_html_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ Navigation link fjernet")
    
    # 2. Remove template file
    print("2. Fjerner template fil...")
    template_path = "bar/templates/bar/drink_planner.html"
    if os.path.exists(template_path):
        os.remove(template_path)
        print("   ✅ Template fil fjernet")
    
    # 3. Remove models from models.py
    print("3. Fjerner modeller...")
    models_path = "bar/models.py"
    if os.path.exists(models_path):
        with open(models_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove the drink planner models
        content = re.sub(
            r'\n\n# DRINK PLANNER MODELS - Easy to remove if not wanted.*?def __str__\(self\):\s*return f"\{self\.drink\.name\} for \{self\.plan\.name\}"',
            '',
            content,
            flags=re.DOTALL
        )
        
        with open(models_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ Modeller fjernet")
    
    # 4. Remove views from views.py
    print("4. Fjerner views...")
    views_path = "bar/views.py"
    if os.path.exists(views_path):
        with open(views_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove import
        content = content.replace(', DrinkPlan, DrinkPlanItem, DrinkIngredient', '')
        
        # Remove views
        content = re.sub(
            r'\n\n# DRINK PLANNER VIEWS - Easy to remove if not wanted.*?return JsonResponse\(\{\'status\': \'error\', \'message\': \'Invalid request method\'\}\)',
            '',
            content,
            flags=re.DOTALL
        )
        
        with open(views_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ Views fjernet")
    
    # 5. Remove URLs from urls.py
    print("5. Fjerner URLs...")
    urls_path = "bar/urls.py"
    if os.path.exists(urls_path):
        with open(urls_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove URL patterns
        content = re.sub(
            r'\s*# DRINK PLANNER URLs - Easy to remove if not wanted.*?path\(\'create-plan/\', views\.create_drink_plan, name=\'create_drink_plan\'\),',
            '',
            content,
            flags=re.DOTALL
        )
        
        with open(urls_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ URLs fjernet")
    
    # 6. Remove admin registration from admin.py
    print("6. Fjerner admin registrering...")
    admin_path = "bar/admin.py"
    if os.path.exists(admin_path):
        with open(admin_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove import
        content = content.replace(', DrinkPlan, DrinkPlanItem, DrinkIngredient', '')
        
        # Remove admin classes
        content = re.sub(
            r'\n\n# DRINK PLANNER ADMIN - Easy to remove if not wanted.*?inlines = \[DrinkPlanItemInline\]',
            '',
            content,
            flags=re.DOTALL
        )
        
        with open(admin_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ Admin registrering fjernet")
    
    # 7. Instructions for migration
    print("\n7. 📋 Næste skridt:")
    print("   For at fjerne tabellerne fra databasen, kør:")
    print("   python manage.py makemigrations")
    print("   python manage.py migrate")
    print("\n✅ Drink Planner funktionalitet er fjernet!")
    print("   Genstart serveren for at se ændringerne.")

if __name__ == "__main__":
    remove_drink_planner()
